using System.Collections.Generic;
using Coder.ScriptWorkflow.DtoTranslator.Define;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.Services;

/// <summary>
///     节点验证服务
/// </summary>
public class NodeValidationService
{
    private readonly IDictionary<NodeSubmit, ITranslator> cache = new Dictionary<NodeSubmit, ITranslator>();

    /// <summary>
    ///     验证单个节点
    /// </summary>
    /// <param name="nodeSubmit">节点提交数据</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证是否通过</returns>
    public bool ValidateNode(NodeSubmit nodeSubmit, out string errorMessage)
    {
        var translator = NodeTranslatorFactory.GetTranslator(nodeSubmit, cache);
        return translator.Validate(nodeSubmit, out errorMessage);
    }

    /// <summary>
    ///     验证多个节点
    /// </summary>
    /// <param name="nodeSubmits">节点提交数据列表</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证是否通过</returns>
    public bool ValidateNodes(IEnumerable<NodeSubmit> nodeSubmits, out string errorMessage)
    {
        errorMessage = null;

        foreach (var nodeSubmit in nodeSubmits)
            if (!ValidateNode(nodeSubmit, out errorMessage))
                return false;

        return true;
    }

    /// <summary>
    ///     验证工作流中的所有节点
    /// </summary>
    /// <param name="workProcessSubmit">工作流提交数据</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证是否通过</returns>
    public bool ValidateWorkProcessNodes(WorkProcessSubmit workProcessSubmit, out string errorMessage)
    {
        return ValidateNodes(workProcessSubmit.Nodes, out errorMessage);
    }
}
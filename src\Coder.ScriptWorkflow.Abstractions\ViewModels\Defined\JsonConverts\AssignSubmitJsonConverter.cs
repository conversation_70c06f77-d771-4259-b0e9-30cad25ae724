﻿using System;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;

/// <summary>
/// </summary>
public class AssignSubmitJsonConverter : JsonCreationConverter<AssignSubmit>
{
    /// <inheritdoc />
    protected override AssignSubmit Create(Type objectType, JObject jObject)
    {
        if (jObject == null) throw new ArgumentNullException("jObject");
        var typeName = jObject["$type"].Value<string>();
        if (typeName == WorkflowDefineType.ScriptAssigner)
            return new ScriptAssignerSubmit();
        if (typeName == WorkflowDefineType.UsersAssigner)
            return new UsersAssignerSubmit();
        throw new ArgumentOutOfRangeException("$type", jObject["$type"].Value<string>() + " not matcher.");
    }
}
﻿using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Interceptors;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.Tags;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Coder.ScriptWorkflow.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.WorkflowManagers;

/// <summary>
/// </summary>
public class WorkflowManagerTest
{
    private static readonly UserViewModel AdminUserViewModel = new()
    {
        Name = "管理员",
        UserName = "admin"
    };

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<UnitTestAppContext>();
            options.AddJsPlugin<ErrorPlugin>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();

        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options =>
        {
            options.UseLazyLoadingProxies();
            options.UseSqlite($"Data Source={dbFile}.db;");
        });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<UnitTestAppContext>();
        // only for unit-tet
        dbContext.Database.EnsureCreated();

        return sp;
    }

    /// <summary>
    /// </summary>
    /// <param name="sp"></param>
    private static void SimpleAuth(IServiceProvider sp)
    {
        /*
         * 草拟-》审批-》发布
         * 测试用例，审批先拒绝一次，然后再执行同意。
         */
        var wpStore = sp.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp.GetRequiredService<IWorkTaskStore>();

        var workProcess = new WorkProcess("流程1")
        {
            Plugins = new List<string>
            {
                "ServerError"
            },
            Enable = true
        };
        wpStore.AddOrUpdate(workProcess);
        wpStore.SaveChangesAsync().Wait();


        var worktask1 = new WorkTask("草拟", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = @"logger.Info('分配','内容')
return 'user'"
            }
        };
        worktask1.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "提交"
        });
        var worktask2 = new WorkTask("审批", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'auth-user'"
            }
        };
        worktask2.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "同意",
            Script = "processInstance.Form={cmd:'同意'}"
        });
        worktask2.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "拒绝",
            Script = "processInstance.Form={cmd:'拒绝'}"
        });
        var worktask3 = new WorkTask("发布", workProcess)
        {
            Assigner = new ScriptAssigner
            {
                Script = "return 'pub-user'"
            },
            NextNode = new EndNode()
        };
        worktask3.Commands.Add(new WorkTaskScriptCommand
        {
            Name = "发布",
            Script = @"processInstance.Form={cmd:'发布'}"
        });
        wtStore.AddOrUpdate(worktask1);
        wtStore.AddOrUpdate(worktask2);
        wtStore.AddOrUpdate(worktask3);
        wtStore.AddOrUpdate(new StartNode(worktask1, workProcess));
        wtStore.SaveChangesAsync().Wait();

        worktask1.NextNode = worktask2;

        var autCondition = new BoolScriptDecision(worktask3, worktask1)
        {
            Script = "return processInstance.Form.cmd=='同意'"
        };
        worktask2.NextNode = autCondition;

        var publishCondition = new BoolScriptDecision(new EndNode(), worktask1)
        { Script = "return processInstance.Form.cmd=='发布'" };
        worktask3.NextNode = publishCondition;


        wpStore.AddOrUpdate(workProcess);
        wpStore.SaveChangesAsync().Wait();
    }

    /// <summary>
    /// </summary>
    [Fact(DisplayName = "ONSwtart js语法错误。")]
    public void OnStartError()
    {
        var sp = Sp();
        SimpleAuth(sp);
        var wpStore = sp.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp.GetRequiredService<IWorkTaskStore>();
        var saStore = sp.GetRequiredService<IWorkActivityStore>();
        var piStore = sp.GetRequiredService<IProcessInstanceStore>();
        var globalScript = sp.GetRequiredService<GlobalScriptContext>();
        var tagStore = sp.GetRequiredService<TagManager>();
        var logManager = sp.GetRequiredService<WorkflowLogManager>();
        var nodeStore = sp.GetRequiredService<INodeStore>();
        var intceptionManager = sp.GetRequiredService<InterceptorManager>();
        var performer = sp.GetRequiredService<PerformerManager>();
        var transFactory = sp.GetRequiredService<ITransactionFactory>();
        var flowManager = new WorkflowManager(saStore, piStore,
            wpStore, wtStore, logManager, intceptionManager, sp, performer, nodeStore, tagStore,
            globalScript, transFactory);
        var processInstance = flowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = "流程1" }, "admin");
        Assert.NotEqual(0, processInstance.Id);
        processInstance.WorkProcess.OnStart = new WorkProcessScript
        {
            Script = "Json.Prason('error')"
        };
        var result = flowManager.Start(processInstance.Id, AdminUserViewModel);
        Assert.False(result.Success, result.Message);
        var message = @"启动失败。原因：""工作流启动事件""执行失败。
详情：ReferenceError: Variable ""Json"" is not defined at (1:1*4),codeLine:1/1/4";
        Assert.Equal(message.Replace("\r\n", "\n"), result.Message.Replace("\r\n", "\n"));
        Assert.Equal("工作流启动事件", result.ErrorScript.EventName);
        Assert.Equal("Json.Prason('error')", result.ErrorScript.Code);
        Assert.Equal(1, result.ErrorScript.Coordinates.Line);
        Assert.Equal("ReferenceError: Variable \"Json\" is not defined at (1:1*4)", result.ErrorScript.Reason);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void OnStartServerError()
    {
        var sp = Sp();
        SimpleAuth(sp);
        var wpStore = sp.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp.GetRequiredService<IWorkTaskStore>();
        var saStore = sp.GetRequiredService<IWorkActivityStore>();
        var piStore = sp.GetRequiredService<IProcessInstanceStore>();
        var tagStore = sp.GetRequiredService<TagManager>();
        var logManager = sp.GetRequiredService<WorkflowLogManager>();
        var globalScript = sp.GetRequiredService<GlobalScriptContext>();
        var nodeStore = sp.GetRequiredService<INodeStore>();
        var intceptionManager = sp.GetRequiredService<InterceptorManager>();
        var performer = sp.GetRequiredService<PerformerManager>();
        var transFactory = sp.GetRequiredService<ITransactionFactory>();
        var flowManager = new WorkflowManager(saStore, piStore,
            wpStore, wtStore, logManager, intceptionManager, sp, performer, nodeStore, tagStore,
            globalScript, transFactory);
        var processInstance = flowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = "流程1" }, "admin");
        Assert.NotEqual(0, processInstance.Id);
        processInstance.WorkProcess.OnStart = new WorkProcessScript
        {
            Script = @"
var a=1;
ServerError.Execute(a)"
        };
        var result = flowManager.Start(processInstance.Id, AdminUserViewModel);
        Assert.False(result.Success, result.Message);
        var exceptErrorMessage = "启动失败。原因：模块【工作流启动事件】。出错原因:脚本调用的插件出错(500),原因:抛出服务器错误哦.1";
        Assert.Equal(exceptErrorMessage, result.Message);
        Assert.Equal("工作流启动事件", result.ErrorScript.EventName);
        Assert.Equal(processInstance.WorkProcess.OnStart.Script, result.ErrorScript.Code);
        Assert.NotNull(result.ErrorScript);
        Assert.Null(result.ErrorScript.PluginErrorStack);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void Normal()
    {
        var sp = Sp();
        SimpleAuth(sp);
        var userViewModel = new UserViewModel
        {
            UserName = "admin",
            Name = "管理员"
        };

        var wpStore = sp.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp.GetRequiredService<IWorkTaskStore>();
        var saStore = sp.GetRequiredService<IWorkActivityStore>();
        var piStore = sp.GetRequiredService<IProcessInstanceStore>();
        var logManager = sp.GetRequiredService<WorkflowLogManager>();
        var nodeStore = sp.GetRequiredService<INodeStore>();
        var intceptionManager = sp.GetRequiredService<InterceptorManager>();
        var performer = sp.GetRequiredService<PerformerManager>();
        var tagStore = sp.GetRequiredService<TagManager>();
        var globalScript = sp.GetRequiredService<GlobalScriptContext>();
        var transFactory = sp.GetRequiredService<ITransactionFactory>();
        var flowManager = new WorkflowManager(saStore, piStore,
            wpStore, wtStore, logManager, intceptionManager, sp, performer, nodeStore, tagStore,
            globalScript, transFactory);


        var processInstance = flowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = "流程1" }, "admin");
        Assert.NotEqual(0, processInstance.Id);
        flowManager.Start(processInstance.Id, userViewModel);

        var pi = piStore.GetById(processInstance.Id);
        Assert.Equal(ProcessInstanceStatus.Processing, pi.Status);
        //启动流程后第一个是 
        var workActivities = flowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        var wa = workActivities.First();
        Assert.Equal("草拟", wa.WorkTask.Name);
        var vmR = flowManager.Resolve(wa.Id,
            new WorkflowResolveSubmit { Command = wa.WorkTask.Commands.First().Name, Comment = "草拟。" }, userViewModel);
        //批准任务-拒绝。
        workActivities = flowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        Assert.Equal("审批", wa.WorkTask.Name);
        vmR = flowManager.Resolve(wa.Id, new WorkflowResolveSubmit { Command = "拒绝", Comment = "请修正部分内容。" },
            userViewModel);
        Assert.True(vmR.Success);
        //决绝返回第一 stp 还是 变为 草拟。
        workActivities = flowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        Assert.Equal("草拟", wa.WorkTask.Name);

        //第一步修正后再搞
        workActivities = flowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        Assert.Equal("草拟", wa.WorkTask.Name);
        vmR = flowManager.Resolve(wa.Id, new WorkflowResolveSubmit { Command = "提交", Comment = "已经修正。" },
            userViewModel);

        //第二 同意
        workActivities = flowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        vmR = flowManager.Resolve(wa.Id, new WorkflowResolveSubmit { Command = "同意", Comment = "同意" }, userViewModel);

        //第三步
        workActivities = flowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        Assert.Equal("发布", wa.WorkTask.Name);

        vmR = flowManager.Resolve(wa.Id, new WorkflowResolveSubmit { Command = "发布", Comment = "同意。" }, userViewModel);

        Assert.Equal("{\"cmd\":\"发布\"}", processInstance.Form);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void TestPerformerSearcherInTags()
    {
        //TODO: 暂时屏蔽，无法找到原因
        return;
        var sp = Sp();
        SimpleAuth(sp);
        var globalScript = sp.GetRequiredService<GlobalScriptContext>();
        var wpStore = sp.GetRequiredService<IWorkProcessStore>();
        var wtStore = sp.GetRequiredService<IWorkTaskStore>();
        var saStore = sp.GetRequiredService<IWorkActivityStore>();
        var piStore = sp.GetRequiredService<IProcessInstanceStore>();
        var logManager = sp.GetRequiredService<WorkflowLogManager>();
        var nodeStore = sp.GetRequiredService<INodeStore>();
        var intceptionManager = sp.GetRequiredService<InterceptorManager>();
        var performer = sp.GetRequiredService<PerformerManager>();
        var tagStore = sp.GetRequiredService<TagManager>();
        var permissionManager = sp.GetRequiredService<WorkflowPermissionManager>();
        var transFactory = sp.GetRequiredService<ITransactionFactory>();

        var flowManager = new WorkflowManager(saStore, piStore,
            wpStore, wtStore, logManager, intceptionManager, sp, performer, nodeStore, tagStore,
            globalScript, transFactory);

        var wp = wpStore.GetByEffectName("流程1");
        wp.OnStart = new WorkProcessScript();
        wp.OnStart.Script = @"
 addTag('总公司:总经理')
if(processInstance.Creator=='user1')
{
    
    addTag('总公司/财务部:部门经理')
} 
else if(processInstance.Creator=='user2')
{
     addTag('广州分公司/销售部:部门经理')
}
else if(processInstance.Creator=='user3')
{
     addTag('中山分公司/财务部:部门经理')
}
else if(processInstance.Creator=='user4')
{
     addTag('珠海分公司/工程部:部门经理')
}
";

        var processInstance1 =
            flowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = "流程1", Subject = "1" }, "user1");

        var processInstance2 =
            flowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = "流程1", Subject = "2" }, "user2");
        var processInstance3 =
            flowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = "流程1", Subject = "3" }, "user3");
        var processInstance4 =
            flowManager.Create(new CreateProcessInstanceSubmit { WorkProcessName = "流程1", Subject = "4" }, "user4");

        //草稿，没有启动 processInstance1;

        //processInstance process2; 第一个处理。
        flowManager.Start(processInstance2, AdminUserViewModel);
        var workActivities = flowManager.GetWorkActivities(processInstance2.Id, WorkActivityStatus.Processing);
        var wa = workActivities.First();
        var vmR = flowManager.Resolve(wa.Id,
            new WorkflowResolveSubmit { Command = wa.WorkTask.Commands.First().Name, Comment = "草拟。" },
            AdminUserViewModel);

        flowManager.Start(processInstance3, AdminUserViewModel);
        workActivities = flowManager.GetWorkActivities(processInstance3.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        vmR = flowManager.Resolve(wa.Id,
            new WorkflowResolveSubmit { Command = wa.WorkTask.Commands.First().Name, Comment = "草拟。" },
            AdminUserViewModel);

        flowManager.Start(processInstance4, AdminUserViewModel);
        workActivities = flowManager.GetWorkActivities(processInstance4.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        vmR = flowManager.Resolve(wa.Id,
            new WorkflowResolveSubmit { Command = wa.WorkTask.Commands.First().Name, Comment = "草拟。" },
            AdminUserViewModel);


        //processInstance process3 :第3个处理。审核通过。


        workActivities = flowManager.GetWorkActivities(processInstance3.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        vmR = flowManager.Resolve(wa.Id, new WorkflowResolveSubmit { Command = "同意", Comment = "同意。" },
            AdminUserViewModel);

        workActivities = flowManager.GetWorkActivities(processInstance4.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        vmR = flowManager.Resolve(wa.Id, new WorkflowResolveSubmit { Command = "同意", Comment = "同意。" },
            AdminUserViewModel);

        //第四个直接结束
        workActivities = flowManager.GetWorkActivities(processInstance4.Id, WorkActivityStatus.Processing);
        wa = workActivities.First();
        vmR = flowManager.Resolve(wa.Id, new WorkflowResolveSubmit { Command = "发布", Comment = "同意。" },
            AdminUserViewModel);

        //-----------------------------------END--------------------------------数据处理完毕。

        //没有tag 只查询user1自己创建的。
        var user1AccessResultList = piStore.ListAsync(new ProcessInstanceSearcher(), "user1".ToClaimsPrincipal(),
            permissionManager
        ).Result;
        Assert.Single(user1AccessResultList);


        // 总公司老大，可以查看全部在途工单

        var list = piStore.ListAsync(new ProcessInstanceSearcher
        {
            Tags = new[] { "总公司:总经理" }
        }, "总经理".ToClaimsPrincipal(), permissionManager).Result;
        Assert.Equal(3, list.Count());

        list = piStore.ListAsync(new ProcessInstanceSearcher
        {
            Tags = new[] { "广州分公司/销售部:部门经理" }
        }, "总经理111".ToClaimsPrincipal(), permissionManager).Result;
        Assert.Single(list);
        var first = list.First();
        Assert.Equal(first.Id, processInstance2.Id);
    }

    /// <summary>
    /// </summary>
    [Fact]
    public void TestCancel()
    {
    }

    /// <summary>
    /// </summary>
    public class ErrorPlugin : IPlugin
    {
        /// <summary>
        /// </summary>
        public string Name { get; } = "ServerError";

        /// <summary>
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public object GetObject(IWorkflowContext context)
        {
            return this;
        }

        /// <summary>
        /// </summary>
        /// <param name="o"></param>
        public void Dispose(object o)
        {
        }

        /// <summary>
        /// </summary>
        /// <returns></returns>
        public string GetJsDefined()
        {
            return "";
        }

        /// <summary>
        /// </summary>
        /// <param name="a"></param>
        /// <exception cref="ArgumentException"></exception>
        public void Execute(int a)
        {
            throw new ArgumentException("抛出服务器错误哦." + a);
        }
    }
}
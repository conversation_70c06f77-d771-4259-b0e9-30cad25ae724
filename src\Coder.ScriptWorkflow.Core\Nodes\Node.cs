﻿using System;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
///     节点。
/// </summary>
public abstract class Node
{
    protected Node()
    {
    }

    protected Node(WorkProcess wp)
    {
        WorkProcess = wp ?? throw new ArgumentNullException(nameof(wp));
    }

    /// <summary>
    ///     名称
    /// </summary>
    public virtual string Name { get; set; }

    /// <summary>
    /// </summary>
    public int Id { get; set; }


    /// <summary>
    ///     是否自动下一层
    /// </summary>
    public abstract bool Auto { get; set; }

    // NextNode 属性已移除，现在由 SingleOutputNode 和 MultiOutputNode 分别处理

    /// <summary>
    /// </summary>
    public virtual WorkProcess WorkProcess { get; set; }

    /// <summary>
    ///     获取下一个节点 - 抽象方法，由子类实现
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <param name="nextNode"></param>
    /// <returns></returns>
    public abstract bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode);

    /// <summary>
    /// </summary>
    public virtual void Validate()
    {
    }

    public Position Position { get; set; } = new Position();

}

public class Position
{
    public int X { get; set; }

    public int Y { get; set; }
}

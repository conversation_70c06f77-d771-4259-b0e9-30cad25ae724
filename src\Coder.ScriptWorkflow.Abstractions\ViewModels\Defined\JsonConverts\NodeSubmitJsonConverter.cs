﻿using System;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;

/// <summary>
/// </summary>
public class NodeSubmitJsonConverter : JsonCreationConverter<NodeSubmit>
{
    /// <inheritdoc />
    protected override NodeSubmit Create(Type objectType, JObject jObject)
    {
        if (jObject == null) throw new ArgumentNullException("jObject");
        var job = jObject["$type"];
        if (job == null)
            throw new ArgumentOutOfRangeException(nameof(jObject),
                JsonConvert.SerializeObject(jObject) + "没有$type");
        var typeName = job.Value<string>();
        if (typeName == WorkflowDefineType.WorkTask)
            return new WorkTaskSubmit();
        if (typeName ==
            WorkflowDefineType.BoolScriptDecision)
            return new BooleanScriptDecisionSubmit();
        if (typeName == WorkflowDefineType.ConditionScriptDecision
           )
            return new ConditionDecisionSubmit();
        if (typeName ==
            WorkflowDefineType.StartNode)
            return new StartNodeSubmit();
        if (WorkflowDefineType.EndNode == typeName)
            return new EndNodeSubmit();
        if (typeName ==
            WorkflowDefineType.ParallelSplit)
            return new ParallelSplitNodeSubmit();
        if (typeName == WorkflowDefineType.ParallelJoin)
            return new ParallelJoinNodeSubmit();
        throw new ArgumentOutOfRangeException("$type", jObject["$type"].Value<string>() + " not matcher.");
    }
}
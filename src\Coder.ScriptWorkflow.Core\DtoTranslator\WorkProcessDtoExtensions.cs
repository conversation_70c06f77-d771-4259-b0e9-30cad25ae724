﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.DtoTranslator.Define;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Permissions;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.ViewModels.Permissions;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;

// ReSharper disable once CheckNamespace
namespace Coder.ScriptWorkflow;

/// <summary>
/// </summary>
public static class WorkProcessDtoExtensions
{
    /// <summary>
    ///     转化为ViewModel
    /// </summary>
    /// <param name="wp"></param>
    /// <returns></returns>
    public static WorkProcessListItem ToViewModel(this WorkProcess wp)
    {
        if (wp == null) throw new ArgumentNullException(nameof(wp));
        var result = new WorkProcessListItem
        {
            Name = wp.Name,
            Publish = wp.Enable,
            Id = wp.Id,
            Version = wp.Version,
            Creator = wp.Creator,
            Group = wp.Group,
            Icon = wp.Icon,
            Abbr = wp.Abbr,
            Comment = wp.Comment
        };
        return result;
    }

    /// <summary>
    ///     把submit的数据赋值到workProcess对象中。
    /// </summary>
    /// <param name="workProcessSubmit"></param>
    /// <param name="workProcess"></param>
    /// <param name="context"></param>
    /// <param name="removeNodes"></param>
    public static void FillTo(this WorkProcessSubmit workProcessSubmit, WorkProcess workProcess, WorkflowSubmitContext context, out IEnumerable<Node> removeNodes)
    {
        workProcess.Name = workProcessSubmit.Name;
        workProcess.Version = workProcessSubmit.Version;
        workProcess.FormDesign = workProcessSubmit.FormDesign;
        workProcess.FormManageDesign = workProcessSubmit.FormManageDesign;
        workProcess.Abbr = workProcessSubmit.Abbr;
        workProcess.Comment = workProcessSubmit.Comment;
        workProcess.Icon = workProcessSubmit.Icon;
        workProcess.FormTypeScriptDefined = workProcessSubmit.FormTypeScriptDefined;
        workProcess.UpdateTimeOffset = DateTime.Now;
        workProcess.LogLevel = workProcessSubmit.LogLevel;
        workProcess.CanBeDeleteWorkActivityCount = workProcessSubmit.CanBeDeleteWorkActivityCount;
        workProcess.Group = workProcessSubmit.Group;
      
        workProcess.OnComplete ??= new WorkProcessScript();
        workProcess.OnComplete.Script = workProcessSubmit.OnCompleteScript;
      
        workProcess.OnStart ??= new WorkProcessScript();
        workProcess.OnStart.Script = workProcessSubmit.OnStartScript;
       
        
        workProcess.OnCancel ??= new WorkProcessScript();
        workProcess.OnCancel.Script = workProcessSubmit.OnCancelScript;
       

        workProcess.Configurations.Clear();
        workProcess.Configurations = workProcessSubmit.Configurations;


        workProcess.Enable = workProcessSubmit.Enable;
        workProcess.Prefix = workProcessSubmit.Prefix;
        workProcess.Creator = workProcessSubmit.Creator;

        workProcess.GlobalScript = workProcessSubmit.GlobalScript;
        if (workProcessSubmit.Plugins != null)
        {
            workProcess.Plugins.Clear();
            foreach (var plugin in workProcessSubmit.Plugins) workProcess.Plugins.Add(plugin);
        }

        //build nextNodes;
        //var startNodeFactory = new StartNodeTranslator();
        //workflow.StartNode = startNodeFactory.GetOrCreate(viewModel.StartNode, context);

        var submitMaping = new Dictionary<NodeSubmit, Node>();
        var removeNodesMap = context.Nodes.ToDictionary(_ => _.Id, _ => _); //将会被删除的节点。

        var translatorCache = new Dictionary<NodeSubmit, ITranslator>();

        //创建有关节点
        foreach (var submit in workProcessSubmit.Nodes)
        {
            var factory = NodeTranslatorFactory.GetTranslator(submit, translatorCache);

            var node = factory.GetOrCreate(submit, context);
            node.WorkProcess = workProcess;
            context.TryAdd(node);
            submitMaping.Add(submit, node);

            if (node.Id != 0) removeNodesMap.Remove(node.Id);
        }

        removeNodes = removeNodesMap.Values;
        //workflow.StartNode.NextNode = submitMaping.Values.First(_ => _.Name == viewModel.StartNode.NextNodeName);
        foreach (var submit in workProcessSubmit.Nodes)
        {
            var factory = NodeTranslatorFactory.GetTranslator(submit,translatorCache);
            var node = submitMaping[submit];
            if (node is EndNode)
                continue;
            factory.BuildRelation(context, node, submit);
        }

        workProcess.UpdateTimeOffset = DateTime.Now;
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    /// <param name="nodes"></param>
    /// <returns></returns>
    public static WorkProcessSubmit ToSubmitViewModel(this WorkProcess workProcess, IEnumerable<Node> nodes)
    {
        if (workProcess == null) throw new ArgumentNullException(nameof(workProcess));

        var viewModel = new WorkProcessSubmit
        {
            Name = workProcess.Name,
            Id = workProcess.Id,
            Group = workProcess.Group,
            LogLevel = workProcess.LogLevel,
            Version = workProcess.Version,
            OnCompleteScript = workProcess.OnComplete?.Script,
            OnCancelScript = workProcess.OnCancel?.Script,
            OnStartScript = workProcess.OnStart?.Script,
            FormTypeScriptDefined = workProcess.FormTypeScriptDefined,
            GlobalScript = workProcess.GlobalScript,
            Configurations = workProcess.Configurations,
            FormDesign = workProcess.FormDesign,
            FormManageDesign = workProcess.FormManageDesign,
            Enable = workProcess.Enable,
            Prefix = workProcess.Prefix,
            UpdateTimeOffset = workProcess.UpdateTimeOffset?.ToString("yyyy-MM-dd HH:mm"),
            CanBeDeleteWorkActivityCount = workProcess.CanBeDeleteWorkActivityCount,
            Abbr = workProcess.Abbr,
            Icon = workProcess.Icon,
            Comment = workProcess.Comment,
            
        };


        foreach (var node in nodes)
        {
            var nodeSubmit = NodeTranslatorFactory.ToViewModel(node);
            viewModel.Nodes.Add(nodeSubmit);
        }

        viewModel.Plugins = workProcess.Plugins?.ToArray();


        return viewModel;
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    /// <param name="nodes"></param>
    /// <returns></returns>
    public static WorkProcessWithNodesViewModel ToWorkProcessWithNodesViewModel(this WorkProcess workProcess, IEnumerable<Node> nodes)
    {
        var result = new WorkProcessWithNodesViewModel
        {
            Name = workProcess.Name,
            Id = workProcess.Id
        };
        foreach (var node in nodes)
        {
            var nodeViewModel = NodeTranslatorFactory.ToViewModel(node);
            result.Nodes.Add(nodeViewModel);
        }

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="permission"></param>
    /// <returns></returns>
    public static WorkProcessPermissionViewModel ToViewModel(this WorkProcessPermission permission)
    {
        return new WorkProcessPermissionViewModel
        {
            Id = permission.Id,
            ProcessName = permission.ProcessName,
            ManageRoles = permission.ManageRoles?.Split(',',StringSplitOptions.RemoveEmptyEntries),

            Performers = permission.Performers.Select(_ => _.ToViewModel()).ToArray()
        };
    }

    /// <summary>
    /// </summary>
    /// <param name="performer"></param>
    /// <returns></returns>
    public static PermissionPerformerViewModel ToViewModel(this PermissionPerformer performer)
    {
        return new PermissionPerformerViewModel
        {
            Id = performer.Id,
            Name = performer.Name,
            Type = performer.Type
        };
    }

    /// <summary>
    /// </summary>
    /// <param name="wpp"></param>
    /// <param name="submit"></param>
    /// <param name="removePerformers"></param>
    public static void Fill(this WorkProcessPermissionSubmit submit, WorkProcessPermission wpp, out IEnumerable<PermissionPerformer> removePerformers)
    {
        wpp.ProcessName = submit.ProcessName;
        if (submit.ManageRoles != null)
            wpp.ManageRoles = string.Join(',', submit.ManageRoles);

        var cloneArray = wpp.Performers.ToDictionary(_ => _.Id, _ => _);
        removePerformers = cloneArray.Values;
        wpp.Performers.Clear();
        foreach (var submitPerformer in submit.Performers)
        {
            PermissionPerformer performerEntity;
            if (submitPerformer.Id != 0)
            {
                performerEntity = cloneArray[submitPerformer.Id];
                cloneArray.Remove(submitPerformer.Id);
            }
            else
            {
                performerEntity = new PermissionPerformer(wpp);
            }

            performerEntity.Fill(submitPerformer);
            wpp.Performers.Add(performerEntity);
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="vm"></param>
    public static void Fill(this PermissionPerformer entity, PermissionPerformerViewModel vm)
    {
        entity.Name = vm.Name;
        entity.Type = vm.Type;
        ;
    }
}
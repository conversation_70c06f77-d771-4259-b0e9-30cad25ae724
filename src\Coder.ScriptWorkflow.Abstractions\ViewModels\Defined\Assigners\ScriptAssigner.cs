﻿namespace Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

/// <summary>
/// </summary>
public class ScriptAssignerSubmit : AssignSubmit
{
    /// <summary>
    /// </summary>
    public ScriptAssignerSubmit()
    {
    }

    /// <summary>
    ///     设置或者获取脚本
    /// </summary>

    public string Script { get; set; }

    ///// <inheritdoc />
    //protected override Assigner Create()
    //{
    //    return new ScriptAssigner();
    //}

    ///// <inheritdoc />
    //protected override void Fill(Assigner assigner)
    //{
    //    var sa = (ScriptAssigner) assigner;
    //    sa.Script = Script;
    //}

    ///// <inheritdoc />
    //protected override bool TypeIsMatch(Assigner assigner)
    //{
    //    return assigner is ScriptAssigner;
    //}
    //}

    protected override string ConsType => WorkflowDefineType.ScriptAssigner;

    /// <summary>
    /// </summary>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public override bool Validate(out string errorMessage)
    {
        errorMessage = null;
        if (string.IsNullOrEmpty(Script))
        {
            errorMessage = "用户派发脚本不能为空.";
            return false;
        }

        return true;
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.WorkflowManagers.ParallelNodeTest.simple_one_done;

public class ParallelNodeTest
{
    private static readonly UserViewModel AdminUserViewModel = new()
    {
        Name = "管理员",
        UserName = "admin"
    };

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<UnitTestAppContext>();
            options.AddJsPlugin<WorkflowManagerTest.ErrorPlugin>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();

        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options =>
        {
            options.UseLazyLoadingProxies();
            options.UseSqlite($"Data Source={dbFile}.db;");
        });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<UnitTestAppContext>();
        // only for unit-tet
        dbContext.Database.EnsureCreated();

        return sp;
    }


    public SwfResult<SaveWorkProcessResult> CreateWorkProcess(out IServiceProvider serviceProvider)
    {
        serviceProvider = Sp();
        var workProcess = new WorkProcessSubmit { Name = "请假单", Prefix = "T_" };
        var assignm = new UsersAssignerSubmit
        {
            AssignScopeType = AssignScopeType.AllOfThem,
            Performers = new List<PerformerSubmit>
            {
                new()
                {
                    Name = "管理员",
                    Key = "admin",
                    Type = PerformerType.User
                }
            }
        };
        var startNode = new StartNodeSubmit
        {
            NextNodeName = "经办人填单"
        };
        var end = new EndNodeSubmit();


        var 经办人填单 = new WorkTaskSubmit
        {
            Name = "经办人填单",
            NextNodeName = "分配任务",
            Assigner = assignm,
            Commands = new List<WorkTaskCommandSubmit>
            {
                new WorkTaskScriptCommandSubmit
                {
                    Name = "同意"
                }
            }
        };

        var parallelNode = new ParallelSplitNodeSubmit
        {
            Name = "分配任务",
            NextNodeNames = ["部门经理", "分公司审批"]
        };
        var 部门经理 = new WorkTaskSubmit
        {
            Assigner = assignm,
            Name = "部门经理",
            NextNodeName = "汇总任务",
            Commands = new List<WorkTaskCommandSubmit>
            {
                new WorkTaskScriptCommandSubmit
                {
                    Name = "同意"
                }
            }
        };

        var 分公司审批 = new WorkTaskSubmit
        {
            Assigner = assignm,
            NextNodeName = "汇总任务",
            Name = "分公司审批",
            Commands = new List<WorkTaskCommandSubmit>
            {
                new WorkTaskScriptCommandSubmit
                {
                    Name = "同意"
                }
            }
        };
        var joinWorkProcess = new ParallelJoinNodeSubmit
        {
            Name = "汇总任务",
            NextNodeName = "经办人查看审批结构",
            JoinCondition = JoinConditionSubmit.Any,
            WaitForWorkTasks =
            [
                分公司审批.Name,部门经理.Name
            ]
        };

        var 经办人查看审批结果 = new WorkTaskSubmit
        {
            Assigner = assignm,
            Name = "经办人查看审批结构",
            NextNodeName = "结束",
            Commands = new List<WorkTaskCommandSubmit>
            {
                new WorkTaskScriptCommandSubmit
                {
                    Name = "同意"
                }
            },
        };


        workProcess.Nodes.Add(startNode);
        workProcess.Nodes.Add(end);
        workProcess.Nodes.Add(joinWorkProcess);
        workProcess.Nodes.Add(经办人填单);
        workProcess.Nodes.Add(parallelNode);
        workProcess.Nodes.Add(部门经理);
        //workProcess.Nodes.Add(组长审批);
        workProcess.Nodes.Add(分公司审批);
        workProcess.Nodes.Add(经办人查看审批结果);


        var manage = serviceProvider.GetRequiredService<WorkflowDefinedManager>();
        workProcess.Enable = true;
        var result = manage.Save(workProcess)
            ;

        Assert.True(result.Success, result.Message);
        Assert.NotEqual(0, result.Data.Id);

        return result;
    }
    /// <summary>
    /// Join 节点。任意一个完成都完成。
    /// </summary>

    [Fact]
    public void Test()
    {
        var t = CreateWorkProcess(out var serviceProvider);
        var workFlowManager = serviceProvider.GetService<WorkflowManager>();
        var admin = new UserViewModel()
        {
            UserName = "admin",
            Name = "管理员"
        };
        var 同意 = new WorkflowResolveSubmit()
        {
            Command = "同意"
        };
        var createResult = workFlowManager.Create(new CreateProcessInstanceSubmit
        {
            WorkProcessName = "请假单",
            CreateUser = "admin",
            Start = true,
        }, "admin");

        var startResult = workFlowManager.Start(createResult, admin);


        var resolveResult = workFlowManager.Resolve(startResult.WorkActivities.First().WorkActivityId, 同意, admin);


        Assert.True(resolveResult.Success);


        var workActivities = workFlowManager.GetWorkActivities(createResult.Id, WorkActivityStatus.Processing);
        resolveResult = workFlowManager.Resolve(workActivities.First().Id, 同意, admin);
        Assert.True(resolveResult.Success, resolveResult.Message);

        Assert.Equal(WorkActivityStatus.CloseByAdmin, workActivities.Skip(1).First().Status);



        workActivities = workFlowManager.GetWorkActivities(createResult.Id, WorkActivityStatus.Processing);
        var workActivity = workActivities.First();
        Assert.Equal("经办人查看审批结构", workActivity.WorkTask.Name);
        resolveResult = workFlowManager.Resolve(workActivity.Id, 同意, admin);

        Assert.True(resolveResult.IsEnd);

    }
}
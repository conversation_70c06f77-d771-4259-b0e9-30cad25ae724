using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace Coder.ScriptWorkflow.Migrations.Mssql;

/// <summary>
/// 设计时 DbContext 工厂，用于 EF Core 迁移工具
/// </summary>
public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
{
    /// <summary>
    /// 创建 ApplicationDbContext 实例
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>ApplicationDbContext 实例</returns>
    public ApplicationDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
        
        // 使用 SQL Server 连接字符串
        // 这里使用一个默认的连接字符串，实际使用时会被覆盖
        optionsBuilder.UseSqlServer("Server=(localdb)\\mssqllocaldb;Database=ScriptWorkflow;Trusted_Connection=true;MultipleActiveResultSets=true",
            b => b.MigrationsAssembly("Coder.ScriptWorkflow.Migrations.Mssql"));
        
        return new ApplicationDbContext(optionsBuilder.Options);
    }
}

﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.DtoTranslator.Define.Decisions;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

internal static class NodeTranslatorFactory
{

    internal static ITranslator GetTranslator(NodeSubmit submit, IDictionary<NodeSubmit, ITranslator> cache)
    {
        if (cache.TryGetValue(submit, out var result))
            return result;

        switch (submit)
        {
            case StartNodeSubmit a:
                result = new StartNodeTranslator();
                break;
            case EndNodeSubmit b:
                result = new EndNodeTranslator();
                break;
            case BooleanScriptDecisionSubmit c:
                result = new ScriptDecisionTranslator();
                break;
            case WorkTaskSubmit d:
                result = new WorkTaskTranslator();
                break;
            case ConditionDecisionSubmit:
                result = new ConditionDecisionTranslator();
                break;
            case ParallelSplitNodeSubmit:
                result = new ParallelSplitNodeTranslator();
                break;
            case ParallelJoinNodeSubmit:
                result = new ParallelJoinNodeTranslator();
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(submit), $"{submit.GetType().Name}没有被实现。");
        }

        cache.Add(submit,  result);
        return result;
    }


    internal static NodeSubmit ToViewModel(Node node)
    {
        switch (node)
        {
            case EndNode endNode:
                return new EndNodeTranslator().ToViewModel(endNode);

            case StartNode startNode:
                return new StartNodeTranslator().ToViewModel(startNode);
            case WorkTask wt:
                return new WorkTaskTranslator().ToViewModel(wt);
            case BoolScriptDecision sd:
                return new ScriptDecisionTranslator().ToViewModel(sd);
            case ConditionDecision cd:
                return new ConditionDecisionTranslator().ToViewModel(cd);
            case ParallelSplitNode psn:
                return new ParallelSplitNodeTranslator().ToViewModel(psn);
            case ParallelJoinNode pjn:
                return new ParallelJoinNodeTranslator().ToViewModel(pjn);
            default:
                throw new ArgumentOutOfRangeException(nameof(node), $"{node.GetType().Name}没有被实现。");
        }
    }
}
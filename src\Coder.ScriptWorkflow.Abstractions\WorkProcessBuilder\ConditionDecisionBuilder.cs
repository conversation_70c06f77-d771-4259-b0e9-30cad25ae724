﻿using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.WorkProcessBuilder;

public class ConditionDecisionBuilder : NodeBuilder<ConditionDecisionSubmit>
{
    private readonly WorkProcessBuilder _builder;
    /// <summary>
    /// 
    /// </summary>
    /// <param name="name"></param>
    /// <param name="builder"></param>
    public ConditionDecisionBuilder(string name, WorkProcessBuilder builder) : base(name, builder)
    {
        _builder = builder;
    }

    protected override void SetValueNodeSubmit(ConditionDecisionSubmit submit)
    {
    }

    public ConditionDecisionBuilder MatchWorkTask(string match, string nextNode, out WorkTaskBuilder workTaskBuilder)
    {
        workTaskBuilder = _builder.TryGetWorkTask(nextNode);
        Submit.Settings.Add(new ConditionSettingSubmit
        {
            NodeName = workTaskBuilder.Name
        });

        return this;
    }

    public ConditionDecisionBuilder MatchWorkTask(string match, WorkTaskBuilder workTaskBuilder)
    {
        Submit.Settings.Add(new ConditionSettingSubmit
        {
            NodeName = workTaskBuilder.Name
        });

        return this;
    }
}
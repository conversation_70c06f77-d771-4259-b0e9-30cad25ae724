using System;
using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
/// 并行分支节点 - 将流程分发到多个并行的WorkTask
/// </summary>
public class ParallelSplitNode : MultiOutputNode
{
    // NextNodes 属性已在 MultiOutputNode 基类中定义

    /// <summary>
    /// 构造函数
    /// </summary>
    public ParallelSplitNode()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="workProcess">工作流定义</param>
    public ParallelSplitNode(WorkProcess workProcess) : base(workProcess)
    {
    }

    /// <summary>
    /// 并行分支节点通常不是自动执行的，需要手动触发分支
    /// </summary>
    public override bool Auto { get; set; } = false;

    // TryNextNode 和 GetNextNodes 方法已在 MultiOutputNode 基类中定义

    /// <summary>
    /// 添加下一个节点
    /// </summary>
    /// <param name="node">要添加的节点</param>
    public void AddNextNode(Node node)
    {
        if (node == null)
            throw new ArgumentNullException(nameof(node));
            
        if (!NextNodes.Contains(node))
        {
            NextNodes.Add(node);
        }
    }

    /// <summary>
    /// 移除下一个节点
    /// </summary>
    /// <param name="node">要移除的节点</param>
    public void RemoveNextNode(Node node)
    {
        if (node != null)
        {
            NextNodes.Remove(node);
        }
    }

    /// <summary>
    /// 验证节点配置
    /// </summary>
    public override void Validate()
    {
        base.Validate();
        
        if (!NextNodes.Any())
        {
            throw new WorkflowDefinedException($"并行分支节点 '{Name}' 必须至少有一个下一个节点", Name);
        }

        // 验证所有下一个节点都是WorkTask
        var invalidNodes = NextNodes.Where(n => !(n is WorkTask)).ToList();
        if (invalidNodes.Any())
        {
            var invalidNodeNames = string.Join(", ", invalidNodes.Select(n => n.Name));
            throw new WorkflowDefinedException($"并行分支节点 '{Name}' 的下一个节点必须都是WorkTask，但发现非WorkTask节点: {invalidNodeNames}", Name);
        }
    }
}

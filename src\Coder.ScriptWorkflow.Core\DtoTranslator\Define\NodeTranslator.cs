﻿using System.Linq;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

/// <summary>
/// </summary>
/// <typeparam name="TNodeSubmit"></typeparam>
/// <typeparam name="TNode"></typeparam>
public abstract class NodeTranslator<TNodeSubmit, TNode> : ITranslator
    where TNodeSubmit : NodeSubmit, new()
    where TNode : Node, new()
{
    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <returns></returns>
    public NodeSubmit ToViewModel(Node node)
    {
        return ToViewModel((TNode)node);
    }

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    Node ITranslator.GetOrCreate(NodeSubmit nodeSubmit, WorkflowSubmitContext context)
    {
        return GetOrCreate((TNodeSubmit)nodeSubmit, context);
    }

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public bool Validate(NodeSubmit nodeSubmit, out string errorMessage)
    {
        return Validate((TNodeSubmit)nodeSubmit, out errorMessage);
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="currentNode"></param>
    /// <param name="nodeSubmit"></param>
    public void BuildRelation(WorkflowSubmitContext context, Node currentNode, NodeSubmit nodeSubmit)
    {
        BuildEntityRelation(context, (TNode)currentNode, (TNodeSubmit)nodeSubmit);
    }

    /// <summary>
    ///     把submit得ViewModel 赋值到node。但是不理会节点之间的关系。
    /// </summary>
    /// <param name="node"></param>
    /// <param name="submit"></param>
    /// <param name="context"></param>
    /// <param name="nodeStore"></param>
    protected abstract void FillToEntity(TNode node, TNodeSubmit submit, WorkflowSubmitContext context, INodeStore nodeStore);

    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <returns></returns>
    public TNodeSubmit ToViewModel(TNode node)
    {
        var result = new TNodeSubmit();
        result.Id = node.Id;
        result.Name = node.Name;

        // 根据节点类型设置 NextNodeName
        if (node is SingleOutputNode singleOutput && singleOutput.NextNode != null)
            (result as SingleOutputNodeSubmit).NextNodeName = singleOutput.NextNode.Name;
        FillToViewModel(node, result);
        result.Type = $"{result.GetType().FullName}, Coder.ScriptWorkflow.Abstractions";
        return result;
    }

    /// <summary>
    /// 验证节点提交数据
    /// </summary>
    /// <param name="nodeSubmit">节点提交数据</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证是否通过</returns>
    public virtual bool Validate(TNodeSubmit nodeSubmit, out string errorMessage)
    {
        errorMessage = null;

        // 1. 基础验证（原 NodeSubmit 中的逻辑）
        if (string.IsNullOrWhiteSpace(nodeSubmit.Name))
        {
            errorMessage = $"id={nodeSubmit.Id}的节点名称必须填写，Type :" + nameof(NodeSubmit);
            return false;
        }

        // 2. 根据节点类型进行特定验证
        if (nodeSubmit is SingleOutputNodeSubmit && !(nodeSubmit is EndNodeSubmit))
        {
            // 单输出节点需要验证 NextNodeName（除了结束节点）
            if (string.IsNullOrEmpty((nodeSubmit as SingleOutputNodeSubmit).NextNodeName))
            {
                errorMessage = $"{nodeSubmit.Name}-下一个节点必须填写";
                return false;
            }
        }
        else if (nodeSubmit is MultiOutputNodeSubmit multiOutput)
        {
            // 多输出节点需要验证 NextNodeNames
            if (multiOutput.NextNodeNames == null || !multiOutput.NextNodeNames.Any())
            {
                errorMessage = $"{nodeSubmit.Name}-必须至少指定一个下一个节点";
                return false;
            }
        }

        // 3. 子类特定验证
        return ValidateSpecific(nodeSubmit, out errorMessage);
    }

    /// <summary>
    /// 子类可以重写此方法添加特定验证逻辑
    /// </summary>
    /// <param name="nodeSubmit">节点提交数据</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证是否通过</returns>
    protected virtual bool ValidateSpecific(TNodeSubmit nodeSubmit, out string errorMessage)
    {
        errorMessage = null;
        return true;
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="target"></param>
    protected abstract void FillToViewModel(TNode src, TNodeSubmit target);

    /// <summary>
    /// </summary>
    /// <param name="nodeSubmit"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    public TNode GetOrCreate(TNodeSubmit nodeSubmit, WorkflowSubmitContext context)
    {
        TNode node;


        node = nodeSubmit.Id == 0 || context.OverWrite == false ? new TNode() : context.GetById<TNode>(nodeSubmit.Id);


        node.Name = nodeSubmit.Name;
        node.WorkProcess = context.WorkProcess;
        FillToEntity(node, nodeSubmit, context, context.NodeStore);


        return node;
    }

    /// <summary>
    ///     创建节点之间的关系。
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected virtual void BuildEntityRelation(WorkflowSubmitContext context, TNode node, TNodeSubmit nodeSubmit)
    {
        if (context.WorkProcess.Enable && string.IsNullOrEmpty((nodeSubmit as SingleOutputNodeSubmit).NextNodeName))
            throw new WorkflowDefinedException(nodeSubmit.Name + "需要设置'下一个节点'");

        if ((nodeSubmit as SingleOutputNodeSubmit).NextNodeName != null && node is SingleOutputNode singleOutput)
            singleOutput.NextNode = context.GetNode((nodeSubmit as SingleOutputNodeSubmit).NextNodeName);
    }
}
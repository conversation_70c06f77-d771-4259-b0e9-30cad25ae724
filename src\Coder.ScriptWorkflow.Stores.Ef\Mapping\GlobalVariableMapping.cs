﻿using System;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// 全局变量设置，可以用客户端服务端或者两端都行。
/// </summary>
internal class GlobalVariableMapping : IEntityTypeConfiguration<GlobalVariable>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public GlobalVariableMapping(string prefix)
    {
        _prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<GlobalVariable> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();


        builder.Property(_ => _.Name).HasMaxLength(100);
        builder.Property(_ => _.Variable).HasMaxLength(300);

        builder.ToTable($"{_prefix}_global_variable");
    }
}
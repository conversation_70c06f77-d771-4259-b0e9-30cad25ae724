﻿using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.WorkProcessBuilder;

public class ParallelSplitBuilder : NodeBuilder<ParallelSplitNodeSubmit>
{
    private readonly WorkProcessBuilder _builder;

    private readonly List<string> _nextNodes = new();

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <param name="builder"></param>
    public ParallelSplitBuilder(string name, WorkProcessBuilder builder) : base(name, builder)
    {
        _builder = builder;
    }


    public override WorkTaskBuilder ToWorkTask(string workTaskName, out WorkTaskBuilder workTaskBuilder)
    {
        var nextWorkTaskName
            = _nextNodes.FirstOrDefault(_ => _ == workTaskName);

        if (nextWorkTaskName == null) _nextNodes.Add(workTaskName);

        var result = _builder.TryGetWorkTask(workTaskName);
        NextNodeName = workTaskName;

        workTaskBuilder = result;
        return result;
    }

    protected override void SetValueNodeSubmit(ParallelSplitNodeSubmit submit)
    {
        submit.NextNodeNames = _nextNodes;
    }
}
﻿using System;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;
using Newtonsoft.Json.Linq;

namespace Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;

public class WorkTaskCommandSubmitJsonConverter : JsonCreationConverter<WorkTaskCommandSubmit>
{
  

    protected override WorkTaskCommandSubmit Create(Type objectType, JObject jObject)
    {
        if (jObject == null) throw new ArgumentNullException("jObject");
        var typeName = jObject["$type"].Value<string>();
        if (typeName == WorkflowDefineType.ScriptCommand)
            return new WorkTaskScriptCommandSubmit();
        if (typeName == WorkflowDefineType.PreviousCommand)
            return new PreviousCommandSubmit();
        throw new ArgumentOutOfRangeException("$type", jObject["$type"].Value<string>() + " not matcher.");
    }
}
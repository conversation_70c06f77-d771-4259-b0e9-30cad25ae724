﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Stores;

namespace Coder.ScriptWorkflow;

/// <summary>
/// 工作流定义上下文
/// </summary>
public class WorkflowSubmitContext
{
    private readonly IDictionary<string, Node> _nodes = new Dictionary<string, Node>();

    /// <summary>
    /// </summary>
    /// <param name="workProcess"></param>
    /// <param name="nodeStore"></param>
    public WorkflowSubmitContext(WorkProcess workProcess, INodeStore nodeStore)
    {
        WorkProcess = workProcess ?? throw new ArgumentNullException(nameof(workProcess));
        NodeStore = nodeStore ?? throw new ArgumentNullException(nameof(nodeStore));
        if (workProcess.Id != 0)
            _nodes = nodeStore.GetNodesByWorkProcessAsync(workProcess).Result.ToDictionary(_ => _.Name, _ => _);
    }

    /// <summary>
    ///     获取工作流定义。
    /// </summary>
    public WorkProcess WorkProcess { get; }

    /// <summary>
    /// </summary>
    public bool OverWrite { get; set; }

    /// <summary>
    /// </summary>
    public INodeStore NodeStore { get; set; }

    /// <summary>
    /// </summary>
    public IEnumerable<Node> Nodes => _nodes.Values;


    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public Node GetNode(string name)
    {
        if (name == null) throw new ArgumentNullException(nameof(name));
        if (_nodes.TryGetValue(name, out var result))
            return result;
        throw new ArgumentOutOfRangeException(nameof(name), "节点-" + name + "没有找到");
    }

    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    public void TryAdd(Node node)
    {

        if (node == null) throw new ArgumentNullException(nameof(node));
        if (_nodes.TryGetValue(node.Name, out var existNode))
        {
            if (existNode.Id != node.Id)
                throw new WorkflowDefinedException($"{node.Name}名称重复了。工作任务、判断器、结束节点、开始节点，名称都不能重复。");
            return;
        }

        _nodes.Add(node.Name, node);

        NodeStore.AddOrUpdate(node);
        NodeStore.SaveChangesAsync().Wait();

    }

    ///// <summary>
    /////     Func的返回值，如果不是null，就是代表修正失败，并且内容是错误数据。
    ///// </summary>
    ///// <param name="lazySetting"></param>
    //internal void AddLazySetting(Func<WorkflowSubmitContext, string> lazySetting)
    //{
    //    LazyLoading.Add(lazySetting);
    //}

    //internal void SettingLazySetting()
    //{
    //    var loop = 0;
    //    var lastCount = LazyLoading.Count + 1;
    //    var resultQueue = LoopLazy(new Queue<Func<WorkflowSubmitContext, string>>(LazyLoading));
    //    while (resultQueue.Count != 0 && resultQueue.Count < lastCount)
    //    {
    //        loop++;
    //        lastCount = resultQueue.Count;
    //        resultQueue = LoopLazy(resultQueue);
    //        if (loop > 30)
    //            throw new WorkflowDefinedException("SettingLazySetting module 出现问题，请联系管理员处理。这次保存失败");
    //    }
    //}

    //private Queue<Func<WorkflowSubmitContext, string>> LoopLazy(Queue<Func<WorkflowSubmitContext, string>> queue)
    //{
    //    var lastErrorMessage = "";
    //    var resultQueue = new Queue<Func<WorkflowSubmitContext, string>>();
    //    while (queue.Count != 0)
    //    {
    //        var lazAction = queue.Dequeue();
    //        lastErrorMessage = lazAction(this);
    //        if (!string.IsNullOrEmpty(lastErrorMessage)) resultQueue.Enqueue(lazAction);
    //    }

    //    return resultQueue;
    //}
    public TNode GetById<TNode>(int nodeSubmitId) where TNode : Node, new()
    {
        return (TNode)_nodes.Values.First(_ => _ is TNode && _.Id == nodeSubmitId);
    }
}
using System;
using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
/// 多输出节点基类 - 可以有多个下一个节点
/// 适用于并行分支节点等需要同时指向多个节点的场景
/// </summary>
public abstract class MultiOutputNode : Node
{
    protected MultiOutputNode()
    {
        
    }

    protected MultiOutputNode(WorkProcess workProcess):base(workProcess)
    {
        
    }
    /// <summary>
    /// 下一个节点列表
    /// </summary>
    public virtual IList<Node> NextNodes { get; set; } = new List<Node>();

    /// <summary>
    /// 获取下一个节点 - 对于多输出节点，这个方法返回false
    /// 因为需要特殊处理多个下一个节点
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="nextNode">下一个节点（对于多输出节点，这里返回null）</param>
    /// <returns>返回false表示需要特殊处理</returns>
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        nextNode = null;
        return false; // 让WorkflowManager知道需要特殊处理
    }

    /// <summary>
    /// 获取所有下一个节点
    /// </summary>
    /// <returns>下一个节点列表</returns>
    public virtual IEnumerable<Node> GetNextNodes() => NextNodes;

    /// <summary>
    /// 添加下一个节点
    /// </summary>
    /// <param name="node">要添加的节点</param>
    public virtual void AddNextNode(Node node)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        
        if (!NextNodes.Contains(node))
        {
            NextNodes.Add(node);
        }
    }

    /// <summary>
    /// 移除下一个节点
    /// </summary>
    /// <param name="node">要移除的节点</param>
    /// <returns>是否成功移除</returns>
    public virtual bool RemoveNextNode(Node node)
    {
        return NextNodes.Remove(node);
    }

    /// <summary>
    /// 验证节点配置
    /// </summary>
    public override void Validate()
    {
        base.Validate();
        
        if (!NextNodes.Any())
        {
            throw new WorkflowDefinedException($"多输出节点 '{Name}' 必须至少指定一个下一个节点", Name);
        }
    }
}

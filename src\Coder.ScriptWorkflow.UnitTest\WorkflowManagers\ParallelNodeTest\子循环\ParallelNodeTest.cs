﻿using System;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.WorkProcessBuilder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.WorkflowManagers.ParallelNodeTest.子循环;

public class ParallelNodeTest
{
    private static readonly UserViewModel AdminUserViewModel = new()
    {
        Name = "管理员",
        UserName = "admin"
    };

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<UnitTestAppContext>();
            options.AddJsPlugin<WorkflowManagerTest.ErrorPlugin>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();

        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options =>
        {
            options.UseLazyLoadingProxies();
            options.UseSqlite($"Data Source={dbFile}.db;");
        });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<UnitTestAppContext>();
        // only for unit-tet
        dbContext.Database.EnsureCreated();

        return sp;
    }


    public SwfResult<SaveWorkProcessResult> CreateWorkProcess(out IServiceProvider serviceProvider,
        out string workProcessName)
    {
        workProcessName = Guid.NewGuid().ToString("N");
        serviceProvider = Sp();

        var builder = WorkProcessBuilder.WorkProcessBuilder.Create(workProcessName).Prefix("A_");


        ParallelSplitBuilder 并行任务 = builder.StartTo("初审")
            .Assign("管理员")
            .SetExecCommand("同意")
            .ToSplitNode("并行任务");

        var 终审 = 并行任务
            .ToWorkTask("办公室指派", out var 办公室指派).SetExecCommand("同意").Assign("管理员")
            .ToWorkTask("办公室指派人审理").SetExecCommand("同意").Assign("管理员")
            .ToWorkTask("办公室主管").SetExecCommand("同意").Assign("管理员")
            .ToConditionDecision("办公室是否有结果").MatchWorkTask("否", 办公室指派)
            .ToJoinNode("终审").WaitNode("办公室是否有结果", "销售部是否有结果").JoinType(JoinConditionSubmit.All);

        并行任务
            .ToWorkTask("销售部指派").SetExecCommand("同意").Assign("管理员")
            .ToWorkTask("销售部指派人审理").SetExecCommand("同意").Assign("管理员")
            .ToWorkTask("销售部主管").SetExecCommand("同意").Assign("管理员")
            .ToConditionDecision("销售部是否有结果").MatchWorkTask("否", 办公室指派)
            .ToJoinNode(终审)
            .ToEnd();


        var manage = serviceProvider.GetRequiredService<WorkflowDefinedManager>();
        var workProcess = builder.Build();
        workProcess.Enable = true;
        var result = manage.Save(workProcess);

        Assert.True(result.Success, result.Message);
        Assert.NotEqual(0, result.Data.Id);

        return result;
    }

    /// <summary>
    ///     Join 节点。任意一个完成都完成。
    /// </summary>
    [Fact]
    public void Test()
    {
        var t = CreateWorkProcess(out var serviceProvider, out string workProcessName);
        var workFlowManager = serviceProvider.GetService<WorkflowManager>();
        var admin = new UserViewModel
        {
            UserName = "admin",
            Name = "管理员"
        };
        var 同意 = new WorkflowResolveSubmit
        {
            Command = "同意"
        };
        var processInstance = workFlowManager.Create(new CreateProcessInstanceSubmit
        {
            WorkProcessName = workProcessName,
            CreateUser = "admin",
            Start = true
        }, "admin");

        var startResult = workFlowManager.Start(processInstance, admin);
        var 初审 = startResult.WorkActivities.First();
        Assert.Equal("初审", 初审.WorkTaskName);
        var resolveResult = workFlowManager.Resolve(初审.WorkActivityId, 同意, admin);
        Assert.True(resolveResult.Success);


        var workActivities = workFlowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        Assert.Equal(2, workActivities.Count());
        var 销售部指派 = workActivities.First(_ => _.WorkTask.Name == "销售部指派");
        Assert.NotNull(销售部指派.ParallelTokenGroup?.First());

        var 办公室指派 = workActivities.First(_ => _.WorkTask.Name == "销售部指派");
        Assert.NotNull(办公室指派.ParallelTokenGroup?.First());

        var parallelToken = 办公室指派.ParallelTokenGroup.First();


        Assert.Equal(办公室指派.ParallelTokenGroup, 销售部指派.ParallelTokenGroup);

        foreach (var wa in workActivities)
        {
            workFlowManager.Resolve(wa, 同意, admin);
        }

        workActivities = workFlowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);

        var 销售部指派人审理 = workActivities.FirstOrDefault(_ => _.WorkTask.Name == "销售部指派人审理");
        Assert.Equal(parallelToken, 销售部指派人审理.ParallelTokenGroup.First());

    }
}
﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.WorkProcessBuilder;

public class ParallelJoinBuilder : NodeBuilder<ParallelJoinNodeSubmit>
{
    private readonly WorkProcessBuilder _builder;

    private readonly List<string> waitTaskName = new();

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <param name="builder"></param>
    public ParallelJoinBuilder(string name, WorkProcessBuilder builder) : base(name, builder)
    {
        _builder = builder;
    }

    public ParallelJoinBuilder JoinType(JoinConditionSubmit join, string script = null)
    {
        Submit.JoinCondition = join;
        if (join == JoinConditionSubmit.Custom) Submit.CustomJoinScript = script;
        return this;
    }
    /// <summary>
    /// 设置前置
    /// </summary>
    /// <param name="nodeNames"></param>
    /// <returns></returns>
    public ParallelJoinBuilder WaitNode(params string[] nodeNames)
    {
        waitTaskName.AddRange(nodeNames);
        return this;
    }

    protected override void SetValueNodeSubmit(ParallelJoinNodeSubmit submit)
    {
        submit.WaitForWorkTasks = waitTaskName.ToArray();
    }
}
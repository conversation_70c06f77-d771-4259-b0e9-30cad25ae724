﻿using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
///     结束节点的提交
/// </summary>
public class EndNodeSubmit : SingleOutputNodeSubmit
{
    /// <summary>
    ///     构造函数
    /// </summary>
    public EndNodeSubmit()
    {
        Name = "结束";
    }

    // 结束节点不需要验证 NextNodeName，因为它没有下一个节点
    protected override string ConstType => WorkflowDefineType.EndNode;

    public override string NextNodeName
    {
        get => null;
        set { }
    }
}
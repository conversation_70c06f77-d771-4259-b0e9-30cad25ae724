﻿using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.WorkProcessBuilder;

public class WorkProcessBuilder
{
    private readonly List<ParallelJoinBuilder> _joinBuilders = new();
    private readonly List<ParallelSplitBuilder> _splitBuilders = new();
    private readonly List<WorkTaskBuilder> _taskBuilders = new();
    private readonly List<ConditionDecisionBuilder> _conditionDecisionBuilders = new();
    private StartNodeSubmit _startNode;
    private WorkProcessSubmit _workProcess;

    public EndNodeSubmit EndNode { get; private set; }

    public static WorkProcessBuilder Create(string name)
    {
        var workProcess = new WorkProcessSubmit();
        workProcess.Name = name;

        var startNode = new StartNodeSubmit();
        var endNode = new EndNodeSubmit();
        workProcess.Nodes.Add(startNode);
        workProcess.Nodes.Add(endNode);

        return new WorkProcessBuilder
        {
            _workProcess = workProcess,
            _startNode = startNode,
            EndNode = endNode
        };
    }

    private INodeBuilder TryGet(string name)
    {
        var task =
            _splitBuilders.FirstOrDefault(a => a.Name == name);

        if (task != null) return task;

        var join =
            _joinBuilders.FirstOrDefault(a => a.Name == name);
        if (join != null) return join;

        var split =
            _splitBuilders.FirstOrDefault(a => a.Name == name);
        return split;
    }

    public WorkProcessBuilder Prefix(string prefix)
    {
        _workProcess.Prefix = prefix;
        return this;
    }

    public WorkTaskBuilder StartTo(string name)
    {
        var task = TryGetWorkTask(name);
        _startNode.NextNodeName = name;
        return task;
    }

    public ParallelSplitBuilder TryGetOrCreateSplit(string name)
    {
        var task =
            _splitBuilders.FirstOrDefault(a => a.Name == name);
        if (task == null)
        {
            task = new ParallelSplitBuilder(name, this);
            _splitBuilders.Add(task);
        }

        return task;
    }

    public ConditionDecisionBuilder TryGetOrCreateConditionDecision(string name)
    {
        var task =
            _conditionDecisionBuilders.FirstOrDefault(a => a.Name == name);
        if (task == null)
        {
            task = new ConditionDecisionBuilder(name, this);
            _conditionDecisionBuilders.Add(task);
        }

        return task;
    }

    public ParallelJoinBuilder TryGetParallelJoin(string name)
    {
        var task =
            _joinBuilders.FirstOrDefault(a => a.Name == name);
        if (task == null)
        {
            task = new ParallelJoinBuilder(name, this);
            _joinBuilders.Add(task);
        }

        return task;
    }

    public WorkTaskBuilder TryGetWorkTask(string name)
    {
        var task = _taskBuilders.FirstOrDefault(a => a.Name == name);
        if (task == null)
        {
            task = new WorkTaskBuilder(name, this);
            _taskBuilders.Add(task);
        }

        return task;
    }

    public WorkProcessSubmit Build()
    {
        foreach (var workTask in _taskBuilders) _workProcess.TryAdd(workTask.Build(), out _);

        foreach (var builder in _joinBuilders) _workProcess.TryAdd(builder.Build(), out _);

        foreach (var builder in _splitBuilders) _workProcess.TryAdd(builder.Build(), out _);
        foreach (var builder in _conditionDecisionBuilders) _workProcess.TryAdd(builder.Build(), out _);
        return _workProcess;
    }

    public void Add(WorkTaskBuilder node)
    {
        _taskBuilders.Add(node);
    }
}
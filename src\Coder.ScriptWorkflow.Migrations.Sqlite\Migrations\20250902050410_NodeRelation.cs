﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.ScriptWorkflow.Migrations.Sqlite.Migrations
{
    /// <inheritdoc />
    public partial class NodeRelation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_swf_node_swf_node_MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.DropIndex(
                name: "IX_swf_node_MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.CreateTable(
                name: "NodeRelations",
                columns: table => new
                {
                    MultiOutputNodeId = table.Column<int>(type: "INTEGER", nullable: false),
                    NextNodesId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NodeRelations", x => new { x.MultiOutputNodeId, x.NextNodesId });
                    table.ForeignKey(
                        name: "FK_NodeRelations_swf_node_MultiOutputNodeId",
                        column: x => x.MultiOutputNodeId,
                        principalTable: "swf_node",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_NodeRelations_swf_node_NextNodesId",
                        column: x => x.NextNodesId,
                        principalTable: "swf_node",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_NodeRelations_NextNodesId",
                table: "NodeRelations",
                column: "NextNodesId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "NodeRelations");

            migrationBuilder.AddColumn<int>(
                name: "MultiOutputNodeId",
                table: "swf_node",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_MultiOutputNodeId",
                table: "swf_node",
                column: "MultiOutputNodeId");

            migrationBuilder.AddForeignKey(
                name: "FK_swf_node_swf_node_MultiOutputNodeId",
                table: "swf_node",
                column: "MultiOutputNodeId",
                principalTable: "swf_node",
                principalColumn: "Id");
        }
    }
}

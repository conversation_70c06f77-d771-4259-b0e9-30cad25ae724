﻿using System;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class GlobalScriptItemMapping : IEntityTypeConfiguration<GlobalScriptItem>
{
    private readonly string _prefix;

    /// <summary>
    /// </summary>
    /// <param name="prefix"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public GlobalScriptItemMapping(string prefix)
    {
        _prefix = prefix ?? throw new ArgumentNullException(nameof(prefix));
    }

    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<GlobalScriptItem> builder)
    {
        builder.HasKey(_ => _.Id);
        builder.Property(_ => _.Id).ValueGeneratedOnAdd();


        builder.Property(_ => _.Name).HasMaxLength(100);
        builder.Property(_ => _.Script).HasColumnType("text");

        builder.ToTable($"{_prefix}_globalScript");
    }
}
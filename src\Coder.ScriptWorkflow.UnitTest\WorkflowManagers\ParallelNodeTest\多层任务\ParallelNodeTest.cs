﻿using System;
using System.Linq;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.UnitTest.Mockers;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Coder.ScriptWorkflow.UnitTest.WorkflowManagers.ParallelNodeTest.多层任务;

public class 多并行人物
{
    private static readonly UserViewModel AdminUserViewModel = new()
    {
        Name = "管理员",
        UserName = "admin"
    };

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static IServiceProvider Sp()
    {
        var service = new ServiceCollection();
        service.AddMemoryCache();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();
        service.AddScriptWorkflowServices(options =>
        {
            options.FileSystemHost = "http://127.0.0.1";
            options.AddEfStores<UnitTestAppContext>();
            options.AddJsPlugin<WorkflowManagerTest.ErrorPlugin>();
        });
        service.AddScoped<IPerformerQueryStore, DemoPerformerQueryStore>();
        service.AddTransient<IDebuggerPusher, EmptyDebuggerPusher>();

        var dbFile = Guid.NewGuid().ToString("N");
        service.AddDbContext<UnitTestAppContext>(options =>
        {
            options.UseLazyLoadingProxies();
            options.UseSqlite($"Data Source={dbFile}.db;");
        });

        // OnConfigDbContext(service);

        var sp = service.BuildServiceProvider();
        using var scope = sp.CreateScope();
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<UnitTestAppContext>();
        // only for unit-tet
        dbContext.Database.EnsureCreated();

        return sp;
    }


    public SwfResult<SaveWorkProcessResult> CreateWorkProcess(out IServiceProvider serviceProvider,
        out string workProcessName)
    {
        workProcessName = Guid.NewGuid().ToString("N");
        serviceProvider = Sp();

        var builder = WorkProcessBuilder.WorkProcessBuilder.Create(workProcessName).Prefix("A_");


        var 并行任务1 = builder.StartTo("初审")
            .Assign("管理员")
            .SetExecCommand("同意")
            .ToSplitNode("并行任务1");

        并行任务1
            .ToWorkTask("WorkTask1").SetExecCommand("同意").Assign("管理员")
            .ToJoinNode("Join1").ToEnd();


        var 并行任务2 = 并行任务1.ToWorkTask("WorkTask2").SetExecCommand("同意").Assign("管理员")
            .ToSplitNode("并行任务2");

        并行任务2.ToWorkTask("WorkTask2.1").SetExecCommand("同意").Assign("管理员").ToJoinNode("Join2").ToJoinNode("Join1");
        并行任务2.ToWorkTask("WorkTask2.2").SetExecCommand("同意").Assign("管理员").ToJoinNode("Join2");


        var manage = serviceProvider.GetRequiredService<WorkflowDefinedManager>();
        var workProcess = builder.Build();
        workProcess.Enable = true;
        var result = manage.Save(workProcess);

        Assert.True(result.Success, result.Message);
        Assert.NotEqual(0, result.Data.Id);

        return result;
    }

    /// <summary>
    ///     Join 节点。任意一个完成都完成。
    /// </summary>
    [Fact]
    public void Test()
    {
        var t = CreateWorkProcess(out var serviceProvider, out var workProcessName);
        var workFlowManager = serviceProvider.GetService<WorkflowManager>();
        var admin = new UserViewModel
        {
            UserName = "admin",
            Name = "管理员"
        };
        var 同意 = new WorkflowResolveSubmit
        {
            Command = "同意"
        };
        var processInstance = workFlowManager.Create(new CreateProcessInstanceSubmit
        {
            WorkProcessName = workProcessName,
            CreateUser = "admin",
            Start = true
        }, "admin");

        var startResult = workFlowManager.Start(processInstance, admin);
        var 初审 = startResult.WorkActivities.First();
        Assert.Equal("初审", 初审.WorkTaskName);
        var resolveResult = workFlowManager.Resolve(初审.WorkActivityId, 同意, admin);
        Assert.True(resolveResult.Success);


        var workActivities = workFlowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        Assert.Equal(2, workActivities.Count());

        var wa_workTask2 = workActivities.First(a => a.WorkTask.Name == "WorkTask2");
        resolveResult = workFlowManager.Resolve(wa_workTask2, 同意, admin);
        Assert.True(resolveResult.Success);

        workActivities = workFlowManager.GetWorkActivities(processInstance.Id, WorkActivityStatus.Processing);
        Assert.Equal(3, workActivities.Count());

        var wa_WorkTask2_1 = workActivities.First(_ => _.WorkTask.Name == "WorkTask2.1");
        Assert.Equal(11, wa_WorkTask2_1.ParallelTokenGroup.Length);
        Assert.True(wa_WorkTask2_1.ParallelTokenGroup.StartsWith(wa_workTask2.ParallelTokenGroup));
    }
}
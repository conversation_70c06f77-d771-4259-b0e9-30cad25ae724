﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.Decisions;

internal class ConditionDecisionTranslator : NodeTranslator<ConditionDecisionSubmit, ConditionDecision>
{
    private readonly Dictionary<string, ConditionSetting> _conditionsMap = new();

    protected override void FillToEntity(ConditionDecision node, ConditionDecisionSubmit submit,
        WorkflowSubmitContext context,
        INodeStore nodeStore)
    {
        node.Script = submit.Script;
        node.MatchDescription = submit.MatchDescription;

        foreach (var conditionSettingSubmit in submit.Settings)
        {
            var nodeSetting = (conditionSettingSubmit.Id != 0
                                  ? node.Settings.FirstOrDefault(x => x.Id == conditionSettingSubmit.Id)
                                  : new ConditionSetting()) ??
                              new ConditionSetting();


            nodeSetting.Description = conditionSettingSubmit.Description;
            nodeSetting.MatchValue = conditionSettingSubmit.matchValue;
            node.Settings.Add(nodeSetting);
            _conditionsMap.Add(conditionSettingSubmit.NodeName, nodeSetting);
        }
    }

    protected override void FillToViewModel(ConditionDecision src, ConditionDecisionSubmit target)
    {
        foreach (var setting in src.Settings)
            target.Settings.Add(new ConditionSettingSubmit
            {
                Description = setting.Description,
                Id = setting.Id,
                NodeName = setting?.Node?.Name,
                matchValue = setting.MatchValue
            });

        target.Script = src.Script;
        target.MatchDescription = src.MatchDescription;
        target.NextNodeName = src.NextNode.Name;
        target.Id = src.Id;
        target.Name = src.Name;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, ConditionDecision node,
        ConditionDecisionSubmit nodeSubmit)
    {
        base.BuildEntityRelation(context, node, nodeSubmit);

        foreach (var settingSubmit in nodeSubmit.Settings)
        {
            var settingEntity = _conditionsMap[settingSubmit.NodeName];
            settingEntity.Node = context.GetNode(settingSubmit.NodeName);
        }
    }
}

/// <summary>
/// </summary>
internal class ScriptDecisionTranslator : NodeTranslator<BooleanScriptDecisionSubmit, BoolScriptDecision>
{
    /// <summary>
    /// </summary>
    /// <param name="node"></param>
    /// <param name="submit"></param>
    /// <param name="context"></param>
    /// <param name="nodeStore"></param>
    protected override void FillToEntity(BoolScriptDecision node, BooleanScriptDecisionSubmit submit,
        WorkflowSubmitContext context, INodeStore nodeStore)
    {
        node.Script = submit.Script;
        node.ElseDescription = submit.ElseDescription;
        node.MatchDescription = submit.MatchDescription;
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="target"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    protected override void FillToViewModel(BoolScriptDecision src, BooleanScriptDecisionSubmit target)
    {
        var decision = src;
        if (decision == null)
            throw new ArgumentOutOfRangeException(nameof(src), "node 必须是ScriptDecision");
        target.ElseNodeName = decision?.ElseNode?.Name;
        target.Script = decision.Script;
        target.ElseDescription = decision.ElseDescription;
        target.MatchDescription = decision.MatchDescription;
    }

    /// <summary>
    ///     验证脚本决策节点特定逻辑
    /// </summary>
    /// <param name="nodeSubmit">脚本决策节点提交数据</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证是否通过</returns>
    protected override bool ValidateSpecific(BooleanScriptDecisionSubmit nodeSubmit, out string errorMessage)
    {
        errorMessage = null;

        // 验证脚本
        if (string.IsNullOrEmpty(nodeSubmit.Script))
        {
            errorMessage = $"判断器[{nodeSubmit.Name}]脚本必须填写";
            return false;
        }

        // 验证否决节点
        if (string.IsNullOrEmpty(nodeSubmit.ElseNodeName))
        {
            errorMessage = $"判断器[{nodeSubmit.Name}]否决节点必须填写";
            return false;
        }

        return true;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="node"></param>
    /// <param name="nodeSubmit"></param>
    protected override void BuildEntityRelation(WorkflowSubmitContext context, BoolScriptDecision node,
        BooleanScriptDecisionSubmit nodeSubmit)
    {
        base.BuildEntityRelation(context, node, nodeSubmit);

        // 设置否决节点
        if (!string.IsNullOrEmpty(nodeSubmit.ElseNodeName))
            node.ElseNode = context.GetNode(nodeSubmit.ElseNodeName);
    }
}
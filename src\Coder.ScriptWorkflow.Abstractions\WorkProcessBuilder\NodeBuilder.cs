﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.WorkProcessBuilder;

public abstract class NodeBuilder<T> : INodeBuilder where T : NodeSubmit, new()
{
    private readonly WorkProcessBuilder _builder;
    protected string NextNodeName;
    protected List<string> NextNodeNames = new();
    protected T Submit;

    /// <summary>
    /// </summary>
    /// <param name="name"></param>
    /// <param name="builder"></param>
    protected NodeBuilder(string name, WorkProcessBuilder builder)
    {
        _builder = builder;
        Submit = new T
        {
            Name = name
        };
    }

    public string Name => Submit.Name;


    public virtual WorkTaskBuilder ToWorkTask(string workTaskName)
    {
        return ToWorkTask(workTaskName, out _);
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public ParallelSplitBuilder ToSplitNode(string name)
    {
        var result = _builder.TryGetOrCreateSplit(name);
        NextNodeName = name;
        return result;
    }

    /// <summary>
    ///     连接到JoinNode，帮其返回JoinNode Builder
    /// </summary>
    /// <param name="builder"></param>
    /// <returns></returns>
    public ParallelJoinBuilder ToJoinNode(ParallelJoinBuilder builder)
    {

        NextNodeName = builder.Name;
        builder.WaitNode(this.Name);
        return builder;
    }

    public ParallelJoinBuilder ToJoinNode(string name)
    {
        var result = _builder.TryGetParallelJoin(name);
        return ToJoinNode(result);
    }

    public void ToEnd()
    {
        NextNodeName = _builder.EndNode.Name;
    }

    public virtual WorkTaskBuilder ToWorkTask(string workTaskName, out WorkTaskBuilder workTaskBuilder)
    {
        if (workTaskName == null) throw new ArgumentNullException(nameof(workTaskName));
        var result = _builder.TryGetWorkTask(workTaskName);
        NextNodeName = workTaskName;
        workTaskBuilder = result;
        return result;
    }

    public ConditionDecisionBuilder ToConditionDecision(string name)
    {
        var result = _builder.TryGetOrCreateConditionDecision(name);
        NextNodeName = name;
        return result;
    }


    public T Build()
    {
        if (Submit is SingleOutputNodeSubmit singleOutputNode)
        {
            if (string.IsNullOrWhiteSpace(NextNodeName))
                throw new ArgumentNullException(nameof(NextNodeName), this.Name + "请设置nextNode,入 ToWorkTask/");
            singleOutputNode.NextNodeName = NextNodeName;
        }
        else
        {
            var multiOutput = Submit as MultiOutputNodeSubmit;
            if (!NextNodeNames.Contains(NextNodeName))
                multiOutput.NextNodeNames.Add(NextNodeName);
        }

        SetValueNodeSubmit(Submit);
        return Submit;
    }

    protected abstract void SetValueNodeSubmit(T submit);
}
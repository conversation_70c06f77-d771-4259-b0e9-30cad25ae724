﻿using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.DtoTranslator.Define.Assigners;
using Coder.ScriptWorkflow.DtoTranslator.Define.WorkCommands;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.WorkTaskCommands;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

/// <summary>
/// </summary>
public class WorkTaskTranslator : NodeTranslator<WorkTaskSubmit, WorkTask>
{
    /// <summary>
    /// </summary>
    /// <param name="wt"></param>
    /// <param name="submit"></param>
    /// <param name="context"></param>
    /// <param name="nodeStore"></param>
    protected override void FillToEntity(WorkTask wt, WorkTaskSubmit submit, WorkflowSubmitContext context, INodeStore nodeStore)
    {
        if (submit.Assigner != null)
        {
            var assignerFactory = AssignerTranslator.Create(submit.Assigner);
            wt.Assigner = assignerFactory.Get(submit.Assigner, wt, context);
        }

        var commandIndex = 0;

        var commands = new List<WorkTaskCommand>();
        foreach (var commandSubmit in submit.Commands)
        {
            var commandTranslator = WorkTaskCommandTranslator.GetBy(commandSubmit);

            var command = commandTranslator.FillTo(commandSubmit, wt);
            commands.Add(command);


            if (command.Order == 0)
                command.Order = commandIndex;

            commandIndex++;
        }

        wt.Commands.Clear();
        foreach (var command in commands)
            wt.Commands.Add(command);


        wt.WorkActivityCompleteScript ??= new WorkActivityScript();
        wt.WorkActivityCompleteScript.Script = submit.WorkActivityCompleteScript?.Script;

        wt.WorkTaskStartScript ??= new WorkTaskStartScript();
        wt.WorkTaskStartScript.Script = submit.WorkTaskStartScript?.Script;

        wt.WorkTaskCompleteScript ??= new WorkTaskCompleteScript();
        wt.WorkTaskCompleteScript.Script = submit.WorkTaskCompleteScript?.Script;

        wt.NextTaskPerformers = submit.NextTaskPerformers;
        wt.FormDesign = submit.FormDesign;
        wt.SuggestionComment = submit.SuggestionComment;

        wt.CanGiveUp = submit.CanGiveUp;
    }


    /// <summary>
    /// 验证工作任务提交数据
    /// </summary>
    /// <param name="submit">工作任务提交数据</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证是否通过</returns>
    public override bool Validate(WorkTaskSubmit submit, out string errorMessage)
    {
        var workTask = $"工作任务[{submit.Name}]";
        
        // 调用基类验证（包括基础验证和节点类型验证）
        if (!base.Validate(submit, out errorMessage))
        {
            errorMessage = workTask + errorMessage;
            return false;
        }

        // 工作任务特定验证
        return ValidateSpecific(submit, out errorMessage);
    }

    /// <summary>
    /// 工作任务特定验证逻辑
    /// </summary>
    /// <param name="submit">工作任务提交数据</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证是否通过</returns>
    protected override bool ValidateSpecific(WorkTaskSubmit submit, out string errorMessage)
    {
        var workTask = $"工作任务[{submit.Name}]";
        errorMessage = null;
        
        // 验证命令
        if (!submit.Commands.Any())
        {
            errorMessage = workTask + "[命令]至少需要一个。";
            return false;
        }

        foreach (var cmd in submit.Commands.ToArray())
            if (!cmd.Validate(out errorMessage))
            {
                errorMessage = workTask + errorMessage;
                return false;
            }

        // 验证分配器
        var assignerFactory = AssignerTranslator.Create(submit.Assigner);
        if (!assignerFactory.Validate(submit.Assigner, out errorMessage))
        {
            errorMessage = workTask + errorMessage;
            return false;
        }

        return true;
    }

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="target"></param>
    protected override void FillToViewModel(WorkTask src, WorkTaskSubmit target)
    {
        target.FormDesign = src.FormDesign;
        target.WorkActivityCompleteScript.Script = src.WorkActivityCompleteScript?.Script;
        target.WorkTaskCompleteScript.Script = src.WorkTaskCompleteScript?.Script;
        target.NextTaskPerformers = src.NextTaskPerformers;
        target.WorkTaskStartScript.Script = src.WorkTaskStartScript?.Script;
        target.Auto = src.Auto;
        target.SuggestionComment = src.SuggestionComment;
        target.CanGiveUp = src.CanGiveUp;
        src.SortCommand();
        foreach (var command in src.Commands)
        {
            var viewModel = WorkTaskCommandTranslator.TranslateToViewModel(command);
            target.Commands.Add(viewModel);
        }

        if (src.Assigner != null)
            target.Assigner = AssignerTranslator.Create(src.Assigner).ToViewModel(src.Assigner);
    }
}
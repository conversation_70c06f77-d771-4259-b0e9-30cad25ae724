using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Tags;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow.Stores;

/// <summary>
///     工作流实例（工单）根据work-process 的执行实例。
/// </summary>
/// <typeparam name="T"></typeparam>
public class ProcessInstanceStore<T> : IProcessInstanceStore
    where T : DbContext
{
    private readonly T _dbContext;
    private readonly GlobalScriptContext _globalScriptContext;
    private readonly ILogger<ProcessInstanceStore<T>> _logger;

    private readonly IServiceProvider _serviceProvider;

    private readonly IUserSettingStore _userSettingStore;


    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="dbContext"></param>
    /// <param name="logger"></param>
    /// <param name="serviceProvider"></param>
    /// <param name="userSettingStore"></param>
    /// <param name="globalScriptContext"></param>
    /// <param name="userClient"></param>
    public ProcessInstanceStore(T dbContext, ILogger<ProcessInstanceStore<T>> logger, IServiceProvider serviceProvider,
        IUserSettingStore userSettingStore, GlobalScriptContext globalScriptContext)
    {
        _dbContext = dbContext;
        _logger = logger;

        _serviceProvider = serviceProvider;
        _userSettingStore = userSettingStore;
        _globalScriptContext = globalScriptContext;
    }

    /// <summary>
    /// </summary>
    public IQueryable<ProcessInstance> ProcessInstances => _dbContext.Set<ProcessInstance>();


    /// <summary>
    ///     通过Id获取工作流实例
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public ProcessInstance GetById(int id)
    {
        return ProcessInstances.Include(_ => _.WorkProcess).FirstOrDefault(_ => _.Id == id);
    }

    /// <summary>
    ///     存入持久缓存曾， 等待调用 <see cref="SaveChanges" />  执行持久化。
    /// </summary>
    /// <param name="instance"></param>
    public void AddOrUpdate(ProcessInstance instance)
    {
        _dbContext.Update(instance);
    }

    /// <summary>
    ///     把存入持久化缓存层的对象持久化。
    /// </summary>
    public void SaveChanges()
    {
        _dbContext.SaveChanges();
    }

    /// <summary>
    /// </summary>
    /// <param name="number"></param>
    /// <returns></returns>
    public ProcessInstance Get(string number)
    {
        if (number == null) throw new ArgumentNullException(nameof(number));
        return _dbContext.Set<ProcessInstance>().Include(_ => _.WorkProcess).FirstOrDefault(_ => _.Number == number);
    }

    /// <summary>
    /// </summary>
    /// <param name="wp"></param>
    /// <returns></returns>
    public int CountProcessingWorkflow(int wp)
    {
        return _dbContext.Set<ProcessInstance>()
            .Count(processInstance => processInstance.WorkProcess.Id == wp && (
                processInstance.Status == ProcessInstanceStatus.Processing
                || processInstance.Status == ProcessInstanceStatus.Created
            ));
    }

    /// <summary>
    ///     删除工作流实例，需要执行<see cref="SaveChanges" />确认删除。
    /// </summary>
    /// <param name="instance"></param>
    public void Remove(IEnumerable<ProcessInstance> instance)
    {
        if (instance == null) throw new ArgumentNullException(nameof(instance));
        _dbContext.RemoveRange(instance);
    }

    /// <summary>
    /// </summary>
    /// <param name="instance"></param>
    public void Remove(ProcessInstance instance)
    {
        if (instance == null) throw new ArgumentNullException(nameof(instance));
        _dbContext.Remove(instance);
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="userClaimsPrincipal"></param>
    /// <param name="permissionManager"></param>
    /// <returns></returns>
    public async Task<IEnumerable<InstanceListItemViewModel>> ListAsync(ProcessInstanceSearcher searcher,
        ClaimsPrincipal userClaimsPrincipal, WorkflowPermissionManager permissionManager)
    {
        if (searcher == null) throw new ArgumentNullException(nameof(searcher));
        if (userClaimsPrincipal == null) throw new ArgumentNullException(nameof(userClaimsPrincipal));

        var defaultTags = await MakeSearchDefaultTags(searcher, userClaimsPrincipal);
        //没有默认tag，并且又不是最大的管理员，因此无法访问任何流程。
        if (!defaultTags.Any() && !WorkflowPermissionManager.IsAdmin(userClaimsPrincipal))
            return Array.Empty<InstanceListItemViewModel>();

        var sto = new Stopwatch();
        sto.Start();
        var query = MakeListProcessInstanceQuery(searcher, userClaimsPrincipal, permissionManager, defaultTags, true);

        var processInstances = await query.OrderByDescending(processInstance => processInstance.Priority)
            .ThenByDescending(_ => _.CreateTime)
            .Skip(searcher.GetSkip()).Take(searcher.GetTake())
            .ToListAsync();
        sto.Stop();
        _logger.LogInformation("获取与ProcessInstance,耗时（秒）：" + sto.Elapsed.TotalSeconds);

        var piIds = processInstances.Select(_ => _.Id).ToList();
        var piTagsTask = GetProcessInstanceTags(piIds);

        sto.Start();
        const int baseWorkActivityId = 10000000; //用于排序
        var lastWorkActivitiesQuery = from wa in _dbContext.Set<WorkActivity>()
                                      where piIds.Contains(wa.ProcessInstance.Id)
                                      group wa by new { processInstanceId = wa.ProcessInstance.Id }
            into g
                                      select g.Max(_ => _.Id + (baseWorkActivityId - (int)_.Status));


        var lastWorkActivities1 = _dbContext.Set<WorkActivity>().AsNoTracking().Include(_ => _.WorkTask)
            .Include(_ => _.ProcessInstance).ThenInclude(_ => _.WorkProcess)
            .Where(workActivity => lastWorkActivitiesQuery.ToArray().Contains(workActivity.Id + (baseWorkActivityId - (int)workActivity.Status)))
            .ToList();
        sto.Stop();

        _logger.LogInformation("获取与ProcessInstance相关的工作活动,耗时（秒）：" + sto.Elapsed.TotalSeconds);
        var lastWorkActivities = new Dictionary<int, WorkActivity>(); //processInstance mapping;
        foreach (var wa in lastWorkActivities1)
        {
            if (!lastWorkActivities.TryGetValue(wa.ProcessInstance.Id, out var exist))
            {
                lastWorkActivities.Add(wa.ProcessInstance.Id, wa);
                continue;
            }

            if ((int)wa.Status < (int)exist.Status) lastWorkActivities[wa.ProcessInstance.Id] = wa;
        }

        var result = new List<InstanceListItemViewModel>();
        var empty = new List<ProcessInstanceTagViewModel>();
        var piTagsMapping = piTagsTask.Result;
        foreach (var processInstance in processInstances)
        {
            if (!piTagsMapping.TryGetValue(processInstance.Id, out var tags)) tags = empty;

            result.Add(
                lastWorkActivities.TryGetValue(processInstance.Id, out var wa)
                    ? wa.ToListItem(tags)
                    : processInstance.ToListItem(tags));
        }

        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="userClaimsPrincipal"></param>
    /// <param name="permissionManager"></param>
    /// <returns></returns>
    public async Task<int> CountAsync(ProcessInstanceSearcher searcher, ClaimsPrincipal userClaimsPrincipal,
        WorkflowPermissionManager permissionManager)
    {
        var defaultTags = await MakeSearchDefaultTags(searcher, userClaimsPrincipal);
        //没有默认tag，并且又不是最大的管理员，因此无法访问任何流程。
        if (!defaultTags.Any() && !WorkflowPermissionManager.IsAdmin(userClaimsPrincipal))
            return 0;


        var query = MakeListProcessInstanceQuery(searcher, userClaimsPrincipal, permissionManager,
            defaultTags);
        return await query.CountAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContextProcessInstance"></param>
    /// <returns></returns>
    public async Task<IEnumerable<ProcessInstanceTag>> GetTagsAsync(ProcessInstance workflowContextProcessInstance)
    {
        return await _dbContext.Set<ProcessInstanceTag>()
            .Include(_ => _.Tag)
            .Where(_ => _.ProcessInstance.Id == workflowContextProcessInstance.Id).ToListAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceTag"></param>
    /// <exception cref="ArgumentNullException">item </exception>
    public void RemoveTag(ProcessInstanceTag processInstanceTag)
    {
        if (processInstanceTag == null) throw new ArgumentNullException(nameof(processInstanceTag));
        _dbContext.Remove(processInstanceTag);
    }

    /// <summary>
    /// </summary>
    /// <param name="newProcessInstanceTag"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public void AddOrUpdate(ProcessInstanceTag newProcessInstanceTag)
    {
        if (newProcessInstanceTag == null) throw new ArgumentNullException(nameof(newProcessInstanceTag));
        _dbContext.Update(newProcessInstanceTag);
    }

    /// <summary>
    /// </summary>
    /// <param name="workProcessId"></param>
    /// <returns></returns>
    public Task<bool> HasProcessInstance(int workProcessId)
    {
        return _dbContext.Set<ProcessInstance>().AnyAsync(_ => _.WorkProcess.Id == workProcessId);
    }

    /// <summary>
    /// </summary>
    /// <param name="pi"></param>
    /// <param name="tag"></param>
    /// <returns></returns>
    public ProcessInstanceTag GetTagByTagName(ProcessInstance pi, string tag)
    {
        if (pi == null) throw new ArgumentNullException(nameof(pi));
        if (tag == null) throw new ArgumentNullException(nameof(tag));
        return _dbContext.Set<ProcessInstanceTag>().FirstOrDefault(_ => _.ProcessInstance == pi && _.Tag.Name == tag);
    }

    /// <summary>
    /// </summary>
    /// <param name="piIds"></param>
    /// <returns></returns>
    public async Task<IEnumerable<ProcessInstanceTagViewModel>> GetTagsByIdsAsync(int[] piIds)
    {
        var r = await _dbContext.Set<ProcessInstanceTag>().Include(_ => _.Tag)
            .Where(_ => piIds.Contains(_.ProcessInstance.Id))
            .Select(_ =>
                new ProcessInstanceTagViewModel
                {
                    Color = _.Color,
                    ProcessInstanceId = _.ProcessInstance.Id,
                    Name = _.Tag.Name,
                    CanDelete = _.CanDelete
                }).ToListAsync();
        return r;
    }

    public void RemoveFile(ProcessInstance.FileAttach file)
    {
        if (file == null) throw new ArgumentNullException(nameof(file));
        _dbContext.Set<ProcessInstance.FileAttach>().Remove(file);
    }

    public Task<int> CountAsync(string creator, ProcessInstanceStatus[] status)
    {
        return ProcessInstances.Where(pi => pi.Creator == creator && status.Contains(pi.Status)).CountAsync();
    }

    public Task SaveChangesAsync()
    {
        return _dbContext.SaveChangesAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="currentUser"></param>
    private async Task<IEnumerable<string>> MakeSearchDefaultTags(ProcessInstanceSearcher searcher,
        ClaimsPrincipal currentUser)
    {
        if (WorkflowPermissionManager.IsAdmin(currentUser))
            return Array.Empty<string>();

        var makeTag = await _userSettingStore.GetMakeTagSetting();

        if (makeTag == null) return Array.Empty<string>();
        ;

        var result = makeTag.MakeUserTags(currentUser, _globalScriptContext).ToArray();
        if (_logger.IsEnabled(LogLevel.Information))
            _logger.LogInformation(currentUser.Identity.Name + "获取默认Tag" + string.Join(",", result));
        return result;
    }

    /// <summary>
    /// </summary>
    /// <param name="processInstanceIds"></param>
    /// <returns></returns>
    private Task<Dictionary<int, List<ProcessInstanceTagViewModel>>> GetProcessInstanceTags(
        IEnumerable<int> processInstanceIds)
    {
        return Task.Run(() =>
        {
            using var scope = _serviceProvider.CreateScope();
            var manaer = scope.ServiceProvider.GetRequiredService<TagManager>();
            var sto = new Stopwatch();
            sto.Start();
            var result = manaer.GetTagsByProcessInstanceId(processInstanceIds.ToArray()).Result;
            sto.Stop();
            _logger.LogInformation("获取与ProcessInstance相关的Tag,耗时（秒）：" + sto.Elapsed.TotalSeconds);
            return result;
        });
    }


    /// <summary>
    /// </summary>
    /// <param name="searcher"></param>
    /// <param name="claimsPrincipal"></param>
    /// <param name="workflowPermissionManager"></param>
    /// <param name="includeRelative"></param>
    /// <returns></returns>
    private IQueryable<ProcessInstance> MakeListProcessInstanceQuery(ProcessInstanceSearcher searcher,
        ClaimsPrincipal claimsPrincipal, WorkflowPermissionManager workflowPermissionManager,
        IEnumerable<string> defaultTags,
        bool includeRelative = false)
    {
        // 管理下的模块都可以使用。
        var managerWorkProcessNames = workflowPermissionManager.GetManagerWorkProcess(claimsPrincipal).ToArray();
        if (searcher.Name != null && searcher.Name.Length != 0)
            managerWorkProcessNames = managerWorkProcessNames.Intersect(searcher.Name).ToArray();
        //先确定一个大范围，凡是 创建者是我自己、或者属于DefaultTags,或者我管理下的，都符合要求
        var selectedTags = defaultTags;
        if (searcher.Tags != null && searcher.Tags.Length != 0) selectedTags = selectedTags.Intersect(searcher.Tags);

        var userName = claimsPrincipal.Identity.Name;
        //var processInstanceTagQuery = PredicateBuilder.New<ProcessInstanceTag>(_ =>
        //    _.ProcessInstance.Creator == userName
        //    || (searcher.OwnManage && managerWorkProcessNames.Contains(_.ProcessInstance.WorkProcess.Name))
        //    || !selectedTags.Any() || selectedTags.Contains(_.Tag.Name)
        //);
        var processInstanceTagQuery =
            PredicateBuilder.New<ProcessInstanceTag>();


        processInstanceTagQuery = processInstanceTagQuery.Or(_ => _.ProcessInstance.Creator == userName);
        if (searcher.OwnManage)
            processInstanceTagQuery =
                processInstanceTagQuery.Or(_ => managerWorkProcessNames.Contains(_.ProcessInstance.WorkProcess.Name));

        if (selectedTags.Any())
            processInstanceTagQuery = processInstanceTagQuery.Or(_ => selectedTags.Contains(_.Tag.Name));


        //foreach (var defaultTag in defaultTags)
        //    defaultTagsInSearcherQuery = defaultTagsInSearcherQuery.Or(_ => _.Tag.Name == defaultTag);

        //foreach (var a in managerWorkProcessNames)
        //{
        //    defaultTagsInSearcherQuery = defaultTagsInSearcherQuery.Or(_ => _.ProcessInstance.WorkProcess.Name == a);
        //}

        //var processInstanceTagQuery = defaultTagsInSearcherQuery;


        //var ccQuery = _dbContext.Set<ProcessInstanceDistribution>().Where(_ => _.UserName == currentUser).Select(_ => _.ProcessInstance.Id);
        //流程number
        if (!string.IsNullOrWhiteSpace(searcher.Number))
            processInstanceTagQuery = processInstanceTagQuery?.And(_ => searcher.Number == _.ProcessInstance.Number);

        if (!string.IsNullOrWhiteSpace(searcher.Subject))
            processInstanceTagQuery =
                processInstanceTagQuery?.And(_ => _.ProcessInstance.Subject.Contains(searcher.Subject));

        if (!string.IsNullOrWhiteSpace(searcher.Creator))
            processInstanceTagQuery = processInstanceTagQuery?
                .And(pi => pi.ProcessInstance.Creator.Contains(searcher.Creator) ||
                          pi.ProcessInstance.CreatorName.Contains(searcher.Creator));

        if (searcher.IsDebug == null)
            processInstanceTagQuery = processInstanceTagQuery?
                .And(pi => pi.ProcessInstance.IsDebug == false);
        else
            processInstanceTagQuery = processInstanceTagQuery?
            .And(pi => pi.ProcessInstance.IsDebug == searcher.IsDebug.Value);

        if (searcher.CreateTimeEnd != null && searcher.CreateTimeStart != null)
            processInstanceTagQuery = processInstanceTagQuery.And(pi =>
                pi.ProcessInstance.CreateTime >= searcher.CreateTimeStart &&
                pi.ProcessInstance.CreateTime <= searcher.CreateTimeEnd);
        else if (searcher.CreateTimeEnd != null)
            processInstanceTagQuery =
                processInstanceTagQuery?.And(pi => pi.ProcessInstance.CreateTime <= searcher.CreateTimeEnd);
        else if (searcher.CreateTimeStart != null)
            processInstanceTagQuery =
                processInstanceTagQuery?.And(_ => _.ProcessInstance.CreateTime >= searcher.CreateTimeStart);
        //状态

        if (searcher.Status != null && searcher.Status.Length != 0)
            processInstanceTagQuery =
                processInstanceTagQuery?.And(_ => searcher.Status.Contains(_.ProcessInstance.Status));

        //流程名字
        if (searcher.WorkProcessId != null)
        {
            processInstanceTagQuery =
                processInstanceTagQuery?.And(_ => searcher.WorkProcessId == _.ProcessInstance.WorkProcess.Id);
        }
        else if (searcher.Name != null && searcher.Name.Length != 0)
            processInstanceTagQuery =
                processInstanceTagQuery?.And(_ => searcher.Name.Contains(_.ProcessInstance.WorkProcess.Name));

        if (!string.IsNullOrEmpty(searcher.Creator))
            processInstanceTagQuery =
                processInstanceTagQuery?
                    .And(_ => _.ProcessInstance.CreatorName == searcher.Creator || _.ProcessInstance.Creator == searcher.Creator)

                ;
        processInstanceTagQuery.And(_ => _.ProcessInstance.IsDebug == false);
        //用户自定义的tag 上传

        if (searcher.Tags != null && searcher.Tags.Length != 0)
        {
            var tagQuery = PredicateBuilder.New<ProcessInstanceTag>(false);
            foreach (var tag in searcher.Tags)
            {
                tagQuery = tagQuery.Or(_ => _.Tag.Name == tag);
                //todo: 有个 bug，如果是 org 需要查询 org 下其他org的数据，因此这里需要特殊处理。但是org 是plugin提供数据，过度修改了org的核心部分。这部分以后需要修正。
                if (tag.StartsWith("org:")) tagQuery = tagQuery.Or(_ => _.Tag.Name.Contains(tag));
            }

            processInstanceTagQuery = processInstanceTagQuery.And(tagQuery);
        }

        var result = _dbContext.Set<ProcessInstanceTag>().Where(processInstanceTagQuery);

        return result.Select(_ => _.ProcessInstance).Distinct();
    }

    public async Task<IEnumerable<ProcessInstance>> ListByNumbersAsync(List<string> numbers)
    {
        return await ProcessInstances.Where(pi => numbers.Contains(pi.Number)).ToListAsync();
    }

    public async Task<IEnumerable<ProcessInstance>> ListByIdsAsync(List<int> ids)
    {
        return ProcessInstances.Where(pi => ids.Contains(pi.Id));
    }

    public Task<int> CountByNumbersAsync(List<string> numbers)
    {
        return ProcessInstances.Where(pi => numbers.Contains(pi.Number)).CountAsync();
    }
}
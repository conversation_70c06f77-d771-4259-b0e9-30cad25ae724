using System;
using System.Collections.Generic;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
/// 单输出节点基类 - 只能有一个下一个节点
/// 适用于大部分节点类型：WorkTask、Decision、EndNode、ParallelJoinNode等
/// </summary>
public abstract class SingleOutputNode : Node
{
    protected SingleOutputNode()
    {
        
    }

    protected SingleOutputNode(WorkProcess workProcess):base(workProcess)
    {
        
    }
    /// <summary>
    /// 下一个节点
    /// </summary>
    public virtual Node NextNode { get; set; }

    /// <summary>
    /// 获取下一个节点
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="nextNode">下一个节点</param>
    /// <returns>是否成功获取下一个节点</returns>
    /// <exception cref="WorkflowDefinedException">当没有定义下级节点时抛出</exception>
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        nextNode = NextNode ?? throw new WorkflowDefinedException($"节点 '{Name}' 没有定义下级节点。", Name);
        return true;
    }

    /// <summary>
    /// 验证节点配置
    /// </summary>
    public override void Validate()
    {
        base.Validate();
        
        // 对于单输出节点，通常需要验证是否有下一个节点
        // 子类可以重写此方法来自定义验证逻辑
    }
}

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
/// 单输出节点提交基类 - 只能有一个下一个节点
/// 适用于大部分节点类型：WorkTask、Decision、EndNode、ParallelJoinNode等
/// </summary>
public abstract class SingleOutputNodeSubmit : NodeSubmit
{
    // 继承 NodeSubmit 的 NextNodeName 属性
    // 对于单输出节点，NextNodeName 属性已经足够
    private string _nextNodeName;
    /// <summary>
    ///     下一个节点
    /// </summary>

    public virtual string NextNodeName
    {
        get => _nextNodeName;
        set => _nextNodeName = value?.Trim();
    }

}

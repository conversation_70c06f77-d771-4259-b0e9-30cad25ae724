﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
/// </summary>
public class WorkTaskSubmit : SingleOutputNodeSubmit
{
    /// <summary>
    ///     是否自动执行
    /// </summary>
    public bool Auto { get; set; }

    /// <summary>
    /// </summary>
    public NextTaskPerformers NextTaskPerformers { get; set; } = NextTaskPerformers.Auto;

    /// <summary>
    ///     分配器
    /// </summary>
    public AssignSubmit Assigner { get; set; }

    /// <summary>
    /// </summary>
    public IList<WorkTaskCommandSubmit> Commands { get; set; } = new List<WorkTaskCommandSubmit>();

    /// <summary>
    /// </summary>
    public string FormDesign { get; set; }

    /// <summary>
    ///     wokActivity结束
    /// </summary>
    public WorkTaskScriptSubmit WorkActivityCompleteScript { get; set; } = new();

    /// <summary>
    ///     工作任务结束
    /// </summary>
    public WorkTaskScriptSubmit WorkTaskCompleteScript { get; set; } = new();

    /// <summary>
    ///     工作任务开始脚本
    /// </summary>
    public WorkTaskScriptSubmit WorkTaskStartScript { get; set; } = new();

    /// <summary>
    ///     默认提交建议。
    /// </summary>
    public string SuggestionComment { get; set; }

    /// <summary>
    ///     是否允许放弃工作。
    /// </summary>
    public bool CanGiveUp { get; set; }

    // Validate 方法已迁移到 WorkTaskTranslator 中
    protected override string ConstType => WorkflowDefineType.WorkTask;
}
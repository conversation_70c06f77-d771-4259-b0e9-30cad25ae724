﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.ScriptWorkflow.Migrations.Mssql.Migrations
{
    /// <inheritdoc />
    public partial class parale : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_swf_node_swf_node_NextNodeId",
                table: "swf_node");

            migrationBuilder.DropTable(
                name: "swf_PI_Distribution");

            migrationBuilder.AlterColumn<string>(
                name: "Icon",
                table: "swf_workProcess",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                comment: "图标",
                oldClrType: typeof(string),
                oldType: "nvarchar(20)",
                oldMaxLength: 20,
                oldNullable: true,
                oldComment: "图标");

            migrationBuilder.AddColumn<string>(
                name: "ParallelTokenGroup",
                table: "swf_WorkActivity",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentWorkActivityId",
                table: "swf_WorkActivity",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CustomJoinScript",
                table: "swf_node",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "JoinCondition",
                table: "swf_node",
                type: "int",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MultiOutputNodeId",
                table: "swf_node",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WaitForWorkTasks",
                table: "swf_node",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_MultiOutputNodeId",
                table: "swf_node",
                column: "MultiOutputNodeId");

            migrationBuilder.AddForeignKey(
                name: "FK_swf_node_swf_node_MultiOutputNodeId",
                table: "swf_node",
                column: "MultiOutputNodeId",
                principalTable: "swf_node",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_swf_node_swf_node_NextNodeId",
                table: "swf_node",
                column: "NextNodeId",
                principalTable: "swf_node",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_swf_node_swf_node_MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.DropForeignKey(
                name: "FK_swf_node_swf_node_NextNodeId",
                table: "swf_node");

            migrationBuilder.DropIndex(
                name: "IX_swf_node_MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "ParallelTokenGroup",
                table: "swf_WorkActivity");

            migrationBuilder.DropColumn(
                name: "ParentWorkActivityId",
                table: "swf_WorkActivity");

            migrationBuilder.DropColumn(
                name: "CustomJoinScript",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "JoinCondition",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "WaitForWorkTasks",
                table: "swf_node");

            migrationBuilder.AlterColumn<string>(
                name: "Icon",
                table: "swf_workProcess",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true,
                comment: "图标",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "图标");

            migrationBuilder.CreateTable(
                name: "swf_PI_Distribution",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ProcessInstanceId = table.Column<int>(type: "int", nullable: true),
                    CreateTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    HasRead = table.Column<bool>(type: "bit", nullable: false),
                    ReadTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    UserName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    UserRealName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_PI_Distribution", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId",
                        column: x => x.ProcessInstanceId,
                        principalTable: "swf_processInstance",
                        principalColumn: "Id");
                },
                comment: "工作流程分发记录");

            migrationBuilder.CreateIndex(
                name: "IX_swf_PI_Distribution_ProcessInstanceId",
                table: "swf_PI_Distribution",
                column: "ProcessInstanceId");

            migrationBuilder.AddForeignKey(
                name: "FK_swf_node_swf_node_NextNodeId",
                table: "swf_node",
                column: "NextNodeId",
                principalTable: "swf_node",
                principalColumn: "Id");
        }
    }
}

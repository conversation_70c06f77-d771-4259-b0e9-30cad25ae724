using System.Collections.Generic;
using Coder.ScriptWorkflow.Nodes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// MultiOutputNode 的 EF Core 映射配置
/// </summary>
internal class MultiOutputNodeMapping : IEntityTypeConfiguration<MultiOutputNode>
{
    /// <summary>
    /// 配置 MultiOutputNode 实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<MultiOutputNode> builder)
    {
        builder.HasBaseType<Node>();

        // 配置 NextNodes 多对多关系 - 暂时注释掉，稍后单独配置
        builder.HasMany(n => n.NextNodes)
            .WithMany()
            .UsingEntity("NodeRelations");


    }
}

﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

// ReSharper disable once CheckNamespace
namespace Coder.ScriptWorkflow.ViewModels.Defined;

public class ConditionDecisionSubmit : DecisionSubmit
{
    public string Script { get; set; }
    /// <summary>
    ///     多个设置
    /// </summary>
    public List<ConditionSettingSubmit> Settings { get; set; } = new();

    protected override string ConstType => WorkflowDefineType.ConditionScriptDecision;
}
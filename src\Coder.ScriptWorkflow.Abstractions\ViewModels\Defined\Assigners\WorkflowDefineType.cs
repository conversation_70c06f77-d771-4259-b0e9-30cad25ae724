﻿namespace Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

public class WorkflowDefineType
{
    public static string ScriptAssigner = "ScriptAssigner";
    public static string UsersAssigner = "UsersAssigner";
    public static string StartNode = "StartNode";
    public static string WorkTask = "WorkTask";
    public static string BoolScriptDecision = "BoolScriptDecision";
    public static string ConditionScriptDecision = "ConditionScriptDecision";
    public static string ParallelSplit = "ParallelSplitNode";
    public static string ParallelJoin = "ParallelJoinNoe";
    public static string EndNode = "EndNode";
    public static string PreviousCommand = "PreviousCommand";
    public static string ScriptCommand = "ScriptCommand";
}
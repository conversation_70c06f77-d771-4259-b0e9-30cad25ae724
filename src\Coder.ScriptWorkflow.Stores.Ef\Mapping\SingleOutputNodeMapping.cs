using Coder.ScriptWorkflow.Nodes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// SingleOutputNode 的 EF Core 映射配置
/// </summary>
internal class SingleOutputNodeMapping : IEntityTypeConfiguration<SingleOutputNode>
{
    /// <summary>
    /// 配置 SingleOutputNode 实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<SingleOutputNode> builder)
    {
        builder.HasBaseType<Node>();
        
        // 配置 NextNode 关系
        builder.HasOne(n => n.NextNode)
               .WithMany()
               .HasForeignKey("NextNodeId")
               .OnDelete(DeleteBehavior.SetNull);
    }
}

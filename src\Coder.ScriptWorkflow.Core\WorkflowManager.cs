﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Coder.FileSystem.Clients;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Interceptors;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Performers;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.Plugins.Console;
using Coder.ScriptWorkflow.Scripts.Plugins.Logger;
using Coder.ScriptWorkflow.Scripts.Plugins.WorkflowManagers;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.Stores;
using Coder.ScriptWorkflow.Tags;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Coder.ScriptWorkflow;

/// <summary>
/// 工作流管理器 - 核心功能
/// </summary>
public partial class WorkflowManager
{
    private readonly DebuggerManager _debuggerManager;
    private readonly GlobalScriptContext _globalScriptContext;
    private readonly InterceptorManager _interceptorManager;
    private readonly INodeStore _nodeStore;
    private readonly PerformerManager _performerManager;
    private readonly IServiceProvider _serviceProvider;
    private readonly TagManager _tagManager;
    private readonly ITransactionFactory _transactionFactory;
    private readonly WorkflowLogManager _workflowLogManager;
    private readonly IWorkTaskStore _workTaskStore;

    /// <summary>
    /// 流程实例存储
    /// </summary>
    protected readonly IProcessInstanceStore ProcessInstanceStore;

    /// <summary>
    /// 工作活动存储
    /// </summary>
    protected readonly IWorkActivityStore WorkActivityStore;

    /// <summary>
    /// 工作流程存储
    /// </summary>
    public IWorkProcessStore WorkProcessStore { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public WorkflowManager(
        IWorkActivityStore workActivityStore,
        IProcessInstanceStore processInstanceStore,
        IWorkProcessStore workProcessStore,
        IWorkTaskStore workTaskStore,
        WorkflowLogManager workflowLogManager,
        InterceptorManager interceptorManager,
        IServiceProvider serviceProvider,
        PerformerManager performerManager,
        INodeStore nodeStore,
        TagManager tagManager,
        GlobalScriptContext globalScriptContext,
        ITransactionFactory transactionFactory,
        [AllowNull] DebuggerManager debuggerManager = null)
    {
        WorkProcessStore = workProcessStore ?? throw new ArgumentNullException(nameof(workProcessStore));
        WorkActivityStore = workActivityStore ?? throw new ArgumentNullException(nameof(workActivityStore));
        ProcessInstanceStore = processInstanceStore ?? throw new ArgumentNullException(nameof(processInstanceStore));
        _workTaskStore = workTaskStore ?? throw new ArgumentNullException(nameof(workTaskStore));
        _workflowLogManager = workflowLogManager ?? throw new ArgumentNullException(nameof(workflowLogManager));
        _interceptorManager = interceptorManager ?? throw new ArgumentNullException(nameof(interceptorManager));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _performerManager = performerManager ?? throw new ArgumentNullException(nameof(performerManager));
        _nodeStore = nodeStore ?? throw new ArgumentNullException(nameof(nodeStore));
        _globalScriptContext = globalScriptContext ?? throw new ArgumentNullException(nameof(globalScriptContext));
        _transactionFactory = transactionFactory ?? throw new ArgumentNullException(nameof(transactionFactory));
        _tagManager = tagManager ?? throw new ArgumentNullException(nameof(tagManager));
        _debuggerManager = debuggerManager;
    }

    /// <summary>
    /// 创建工作流程
    /// </summary>
    /// <param name="submit">提交的信息</param>
    /// <param name="userName">用户</param>
    /// <param name="userRealName">用户显示名称</param>
    /// <returns>流程实例</returns>
    public virtual ProcessInstance Create([NotNull] CreateProcessInstanceSubmit submit, [NotNull] string userName, string userRealName = null)
    {
        ValidateCreateParameters(submit, userName);
        
        var workProcess = GetWorkProcess(submit);
        if (workProcess == null)
        {
            throw new WorkflowDefinedException($"工作流 {submit.WorkProcessName} 不存在");
        }

        using var transaction = _transactionFactory.BeginTransactions();
        try
        {
            var instance = CreateProcessInstance(workProcess, userName, userRealName, submit);
            SaveProcessInstance(instance);
            
            LogProcessInstanceCreation(instance, userName);
            transaction.Commit();
            
            return instance;
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }

    /// <summary>
    /// 验证创建参数
    /// </summary>
    private static void ValidateCreateParameters(CreateProcessInstanceSubmit submit, string userName)
    {
        if (submit == null) 
            throw new ArgumentNullException(nameof(submit));
            
        if (string.IsNullOrWhiteSpace(submit.WorkProcessName)) 
            throw new ArgumentNullException(nameof(submit.WorkProcessName));
            
        if (string.IsNullOrWhiteSpace(userName)) 
            throw new ArgumentNullException(nameof(userName));
    }

    /// <summary>
    /// 获取工作流程
    /// </summary>
    private WorkProcess GetWorkProcess(CreateProcessInstanceSubmit submit)
    {
        return submit.WorkProcessId.HasValue
            ? WorkProcessStore.Get(submit.WorkProcessId.Value)
            : WorkProcessStore.GetByEffectName(submit.WorkProcessName);
    }

    /// <summary>
    /// 创建流程实例
    /// </summary>
    private ProcessInstance CreateProcessInstance(WorkProcess workProcess, string userName, string userRealName, CreateProcessInstanceSubmit submit)
    {
        var instance = new ProcessInstance(workProcess, userName);
        
        if (!string.IsNullOrWhiteSpace(userRealName))
        {
            instance.CreatorName = userRealName;
        }

        instance.Subject = submit.Subject ?? workProcess.Name;
        instance.ReplaceForm(submit.Form);
        instance.Priority = submit.Priority;
        instance.IsDebug = submit.IsDebug ?? false;
        
        return instance;
    }

    /// <summary>
    /// 保存流程实例
    /// </summary>
    private void SaveProcessInstance(ProcessInstance instance)
    {
        ProcessInstanceStore.AddOrUpdate(instance);
        ProcessInstanceStore.SaveChanges();
    }

    /// <summary>
    /// 记录流程实例创建日志
    /// </summary>
    private void LogProcessInstanceCreation(ProcessInstance instance, string userName)
    {
        _debuggerManager?.SendMessage(instance, "启动成功");
        _workflowLogManager.LogInfo(instance, WorkflowLogType.System, $"{userName}启动工作成功。");
        _workflowLogManager.Flush();
    }

    /// <summary>
    /// 获取工作活动
    /// </summary>
    public IEnumerable<WorkActivity> GetWorkActivities(int processInstanceId, WorkActivityStatus status, string user = null)
    {
        var query = WorkActivityStore.WorkActivities.Where(activity =>
            activity.ProcessInstance.Id == processInstanceId && 
            activity.Status == status &&
            (user == null || activity.DisposeUser == user));

        return query.ToList();
    }

    /// <summary>
    /// 获取工作流上下文
    /// </summary>
    private IWorkflowContext GetWorkflowContext([NotNull] ProcessInstance processInstance, UserViewModel currentUser, WorkActivity currentWorkActivity = null)
    {
        var nodes = _nodeStore.GetNodesByWorkProcessAsync(processInstance.WorkProcess).Result;
        var plugins = GetPlugins(processInstance);
        var scriptTags = GetScriptTags(processInstance);

        if (currentWorkActivity == null)
        {
            return new WorkflowContext(
                _globalScriptContext, 
                processInstance, 
                nodes, 
                plugins, 
                scriptTags, 
                _workflowLogManager,
                _debuggerManager, 
                currentUser);
        }

        var relatedWorkActivities = WorkActivityStore.GetByGroup(currentWorkActivity.TaskCreatingGroup, currentWorkActivity.Id);
        return new WorkflowContext(
            _globalScriptContext, 
            nodes, 
            currentWorkActivity, 
            relatedWorkActivities, 
            plugins,
            scriptTags, 
            _workflowLogManager, 
            _debuggerManager, 
            currentUser);
    }

    /// <summary>
    /// 获取插件
    /// </summary>
    private IEnumerable<IPlugin> GetPlugins(ProcessInstance processInstance)
    {
        var defaultPlugins = new[]
        {
            LoggerPlugin.PluginName,
            WorkflowManagerPlugin.PluginName,
            JsConsolePlugin.PluginName
        };

        return _serviceProvider.GetServices<IPlugin>()
            .Where(plugin => processInstance.WorkProcess.Plugins.Contains(plugin.Name) || defaultPlugins.Contains(plugin.Name));
    }

    /// <summary>
    /// 获取脚本标签
    /// </summary>
    private List<ScriptTagInfo> GetScriptTags(ProcessInstance processInstance)
    {
        var processInstanceTags = ProcessInstanceStore.GetTagsAsync(processInstance).Result;
        return processInstanceTags.Select(tag => new ScriptTagInfo
        {
            ProcessInstanceTagId = tag.Id,
            TagStatus = TagChanged.Normal,
            TagName = tag.Tag.Name
        }).ToList();
    }

    /// <summary>
    /// 获取工作流上下文
    /// </summary>
    private IWorkflowContext GetWorkflowContext(WorkActivity workActivity, UserViewModel currentUser)
    {
        return GetWorkflowContext(workActivity.ProcessInstance, currentUser, workActivity);
    }

    /// <summary>
    /// 接受工作活动
    /// </summary>
    public SwfResult<AcceptResult> Accept(int workActivityId, UserViewModel currentUser)
    {
        ValidateAcceptParameters(workActivityId);
        
        var workActivity = GetWorkActivityById(workActivityId);
        if (workActivity == null)
        {
            throw new NotFoundWorkActivityException(workActivityId);
        }

        using var transaction = _transactionFactory.BeginTransactions();
        try
        {
            AssignWorkActivity(workActivity, currentUser);
            SaveWorkActivity(workActivity);
            
            using var workflowContext = GetWorkflowContext(workActivity, currentUser);
            NotifyWorkActivityChange(workflowContext, workActivity);
            AddDisposeUserTag(workActivity.ProcessInstance, currentUser.UserName);
            
            transaction.Commit();

            return CreateAcceptResult(workActivity).ToSuccess("成功接受工作。");
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }

    /// <summary>
    /// 验证接受参数
    /// </summary>
    private static void ValidateAcceptParameters(int workActivityId)
    {
        if (workActivityId <= 0)
            throw new ArgumentOutOfRangeException(nameof(workActivityId), "必须大于0");
    }

    /// <summary>
    /// 分配工作活动
    /// </summary>
    private void AssignWorkActivity(WorkActivity workActivity, UserViewModel currentUser)
    {
        workActivity.AssignTo(currentUser.UserName, currentUser.Name);
    }

    /// <summary>
    /// 保存工作活动
    /// </summary>
    private void SaveWorkActivity(WorkActivity workActivity)
    {
        WorkActivityStore.AddOrUpdate(workActivity);
        WorkActivityStore.SaveChanges();
    }

    /// <summary>
    /// 通知工作活动变更
    /// </summary>
    private void NotifyWorkActivityChange(IWorkflowContext workflowContext, WorkActivity workActivity)
    {
        _interceptorManager.OnWorkActivityChanged(workflowContext, workActivity, _serviceProvider);
    }

    /// <summary>
    /// 添加处理用户标签
    /// </summary>
    private void AddDisposeUserTag(ProcessInstance processInstance, string userName)
    {
        _tagManager.AddTag(processInstance, Tag.MakeDisposeUserTag(userName));
    }

    /// <summary>
    /// 创建接受结果
    /// </summary>
    private static AcceptResult CreateAcceptResult(WorkActivity workActivity)
    {
        return new AcceptResult
        {
            DisposeUser = workActivity.DisposeUser,
            AssignTime = workActivity.AssignTime,
            Status = workActivity.Status
        };
    }

    /// <summary>
    /// 暂停流程实例
    /// </summary>
    public ProcessInstance Suspend(int processInstanceId, string comment, UserViewModel userName)
    {
        using var transaction = _transactionFactory.BeginTransactions();
        try
        {
            var instance = GetById(processInstanceId);
            if (instance == null)
                throw new NotFoundProcessInstanceException(processInstanceId);

            using var workflowContext = GetWorkflowContext(instance, userName);
            SuspendProcessInstance(instance, comment);
            SuspendWorkActivities(processInstanceId, workflowContext);
            
            SaveProcessInstance(instance);
            transaction.Commit();
            
            NotifyWorkActivityChanges(workflowContext, processInstanceId);
            
            return instance;
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }

    /// <summary>
    /// 暂停流程实例
    /// </summary>
    private void SuspendProcessInstance(ProcessInstance instance, string comment)
    {
        instance.Suspend(comment);
    }

    /// <summary>
    /// 暂停工作活动
    /// </summary>
    private void SuspendWorkActivities(int processInstanceId, IWorkflowContext workflowContext)
    {
        var inProcessingWorkActivities = WorkActivityStore.GetInProcessWorkActivity(processInstanceId).ToArray();
        foreach (var workActivity in inProcessingWorkActivities)
        {
            workActivity.Suspend();
            WorkActivityStore.AddOrUpdate(workActivity);
        }
    }

    /// <summary>
    /// 通知工作活动变更
    /// </summary>
    private void NotifyWorkActivityChanges(IWorkflowContext workflowContext, int processInstanceId)
    {
        var inProcessingWorkActivities = WorkActivityStore.GetInProcessWorkActivity(processInstanceId);
        foreach (var workActivity in inProcessingWorkActivities)
        {
            _interceptorManager.OnWorkActivityChanged(workflowContext, workActivity, _serviceProvider);
        }
    }

    /// <summary>
    /// 恢复流程实例
    /// </summary>
    public ProcessInstance Resume(int processInstanceId, UserViewModel userName)
    {
        using var transaction = _transactionFactory.BeginTransactions();
        try
        {
            var instance = GetById(processInstanceId);
            if (instance == null)
                throw new NotFoundProcessInstanceException(processInstanceId);

            using var workflowContext = GetWorkflowContext(instance, userName);
            ResumeProcessInstance(instance);
            ResumeWorkActivities(processInstanceId, workflowContext);
            
            SaveProcessInstance(instance);
            transaction.Commit();
            
            NotifyResumedWorkActivities(workflowContext, processInstanceId);
            
            return instance;
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }

    /// <summary>
    /// 恢复流程实例
    /// </summary>
    private void ResumeProcessInstance(ProcessInstance instance)
    {
        instance.Resume();
    }

    /// <summary>
    /// 恢复工作活动
    /// </summary>
    private void ResumeWorkActivities(int processInstanceId, IWorkflowContext workflowContext)
    {
        var suspendProcessWorkActivities = WorkActivityStore.GetSuspendProcessWorkActivity(processInstanceId).ToArray();
        foreach (var workActivity in suspendProcessWorkActivities)
        {
            workActivity.Resume();
            WorkActivityStore.AddOrUpdate(workActivity);
        }
    }

    /// <summary>
    /// 通知恢复的工作活动
    /// </summary>
    private void NotifyResumedWorkActivities(IWorkflowContext workflowContext, int processInstanceId)
    {
        var suspendProcessWorkActivities = WorkActivityStore.GetSuspendProcessWorkActivity(processInstanceId);
        foreach (var workActivity in suspendProcessWorkActivities)
        {
            _interceptorManager.OnWorkActivityChanged(workflowContext, workActivity, _serviceProvider);
        }
    }

    /// <summary>
    /// 放弃工作活动
    /// </summary>
    public WorkActivity Abandon(int workActivityId, UserViewModel userName)
    {
        var workActivity = GetWorkActivityById(workActivityId);
        using var workflowContext = GetWorkflowContext(workActivity, userName);
        
        workActivity.Abandon();
        SaveWorkActivity(workActivity);
        
        _interceptorManager.OnWorkActivityChanged(workflowContext, workActivity, _serviceProvider);
        
        return workActivity;
    }

    /// <summary>
    /// 根据ID获取工作活动
    /// </summary>
    public WorkActivity GetWorkActivityById(int id)
    {
        return WorkActivityStore.GetById(id);
    }

    /// <summary>
    /// 根据ID获取流程实例
    /// </summary>
    public ProcessInstance GetById(int id)
    {
        return ProcessInstanceStore.GetById(id);
    }

    /// <summary>
    /// 启动流程实例
    /// </summary>
    public StartResult Start(int processInstanceId, UserViewModel currentUser)
    {
        if (processInstanceId <= 0) 
            throw new ArgumentOutOfRangeException(nameof(processInstanceId));
            
        var instance = GetById(processInstanceId);
        if (instance == null)
            throw new NotFoundProcessInstanceException(processInstanceId);
            
        return Start(instance, currentUser);
    }

    /// <summary>
    /// 启动流程实例
    /// </summary>
    public StartResult Start(ProcessInstance instance, UserViewModel currentUser)
    {
        if (instance == null) 
            throw new ArgumentNullException(nameof(instance));

        var workflowContext = GetWorkflowContext(instance, currentUser);
        var result = CreateStartResult();
        var processInstanceIsComplete = false;
        try
        {
            if (instance.Id == 0)
            {
                SaveProcessInstance(instance);
            }

         
            StartProcessInstance(instance, workflowContext);
            var lastContext = ProcessStartWorkflow(workflowContext, ref processInstanceIsComplete);
            
            SetStartResult(result, lastContext);
        }
        catch (JsRuntimeException ex)
        {
            return HandleJsRuntimeExceptionInStart(ex, result);
        }
        catch (WorkflowDefinedException ex)
        {
            return HandleWorkflowDefinedExceptionInStart(workflowContext, ex, instance, result);
        }
        catch (Exception ex)
        {
            HandleGenericExceptionInStart(workflowContext, ex);
            throw;
        }
        finally
        {
            _workflowLogManager.Flush();
        }

        PersistentDatabase(workflowContext);
        OnFireEvents(workflowContext, processInstanceIsComplete);

        return result;
    }

    /// <summary>
    /// 创建启动结果
    /// </summary>
    private static StartResult CreateStartResult()
    {
        return new StartResult
        {
            Message = "启动成功。",
            Success = true
        };
    }

    /// <summary>
    /// 启动流程实例
    /// </summary>
    private void StartProcessInstance(ProcessInstance instance, IWorkflowContext workflowContext)
    {
        instance.Start();
        instance.WorkProcess.OnStart?.Invoke(workflowContext, WorkProcess.ModuleOnStart);
        workflowContext.CurrentNode = workflowContext.StartNode;
    }

    /// <summary>
    /// 处理启动工作流
    /// </summary>
    private IWorkflowContext ProcessStartWorkflow(IWorkflowContext workflowContext, ref bool processInstanceIsComplete)
    {
        var lastContext = RunToNextNodeWorkflowContext(workflowContext, CustomerNextWorkTaskSetting.FollowWorkProcessSetting, ref processInstanceIsComplete);
        
        if (lastContext != workflowContext && lastContext.CurrentNode.Auto && !processInstanceIsComplete)
        {
            lastContext = HandlerAutoNode(lastContext, "开始", out processInstanceIsComplete);
        }
        
        return lastContext;
    }

    /// <summary>
    /// 设置启动结果
    /// </summary>
    private void SetStartResult(StartResult result, IWorkflowContext lastContext)
    {
        if (lastContext.AllWorkActivities.Any())
        {
            result.WorkActivities = lastContext.AllWorkActivities.Select(wa => wa.ToSimpleWorkActivity());
        }
    }

    /// <summary>
    /// 处理启动中的JavaScript运行时异常
    /// </summary>
    private StartResult HandleJsRuntimeExceptionInStart(JsRuntimeException ex, StartResult result)
    {
        result.FillCodeResult(ex);
        result.Success = false;
        result.Message = $"启动失败。原因：\"{ex.EventName}\"执行失败。\r\n详情：{ex.Message},codeLine:{ex.Coordinates}";
        return result;
    }

    /// <summary>
    /// 处理启动中的工作流定义异常
    /// </summary>
    private StartResult HandleWorkflowDefinedExceptionInStart(IWorkflowContext workflowContext, WorkflowDefinedException ex, ProcessInstance instance, StartResult result)
    {
        workflowContext.SendMessageDebugInfo(ex.Message, DebuggerType.Error);
        _workflowLogManager.LogError(workflowContext, WorkflowLogType.System, ex, WorkProcess.ModuleOnStart);

        result.ErrorScript = new ScriptError
        {
            Code = instance.WorkProcess.OnStart.Script,
            EventName = WorkProcess.ModuleOnStart,
        };

        result.Success = false;
        result.Message = $"启动失败。原因：{ex.Message}";
        return result;
    }

    /// <summary>
    /// 处理启动中的通用异常
    /// </summary>
    private void HandleGenericExceptionInStart(IWorkflowContext workflowContext, Exception ex)
    {
        _workflowLogManager.LogError(workflowContext, WorkflowLogType.System, ex, WorkProcess.ModuleOnStart);
    }

    /// <summary>
    /// 取消流程实例
    /// </summary>
    public CancelProcessInstanceResult Cancel(int processInstanceId, UserViewModel userName)
    {
        var instance = GetById(processInstanceId);
        if (instance.Status == ProcessInstanceStatus.Cancel || instance.Status == ProcessInstanceStatus.Completed)
        {
            return new CancelProcessInstanceResult
            {
                Success = false,
                Message = $"工作流已经处于{instance.Status},因此不能被取消"
            };
        }

        using var transaction = _transactionFactory.BeginTransactions();
        try
        {
            var workflowContext = GetWorkflowContext(instance, userName);
            CancelProcessInstance(instance, processInstanceId, workflowContext);
            
            SaveProcessInstance(instance);
            WorkActivityStore.SaveChanges();
            
            ExecuteCancelScript(instance, workflowContext);
            _interceptorManager.OnProcessChanged(workflowContext, instance, _serviceProvider);
            
            transaction.Commit();
            
            return CreateCancelSuccessResult(instance);
        }
        catch (JsRuntimeException ex)
        {
            return HandleJsRuntimeExceptionInCancel(ex);
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
        finally
        {
            _workflowLogManager.Flush();
        }
    }

    /// <summary>
    /// 取消流程实例
    /// </summary>
    private void CancelProcessInstance(ProcessInstance instance, int processInstanceId, IWorkflowContext workflowContext)
    {
        instance.Cancel();
        var workActivities = WorkActivityStore.GetInProcessWorkActivity(processInstanceId);
        foreach (var workActivity in workActivities)
        {
            workActivity.Cancel();
            WorkActivityStore.AddOrUpdate(workActivity);
            _interceptorManager.OnWorkActivityChanged(workflowContext, workActivity, _serviceProvider);
        }
    }

    /// <summary>
    /// 执行取消脚本
    /// </summary>
    private void ExecuteCancelScript(ProcessInstance instance, IWorkflowContext workflowContext)
    {
        if (instance.WorkProcess.OnCancel != null && !string.IsNullOrWhiteSpace(instance.WorkProcess.OnCancel.Script))
        {
            using var cancelWorkflowContext = GetWorkflowContext(instance, workflowContext.CurrentUser);
            instance.WorkProcess.OnCancel.Invoke(workflowContext, WorkProcess.ModuleOnCancel);
        }
    }

    /// <summary>
    /// 创建取消成功结果
    /// </summary>
    private static CancelProcessInstanceResult CreateCancelSuccessResult(ProcessInstance instance)
    {
        return new CancelProcessInstanceResult
        {
            Success = true,
            Message = $"取消[{instance.Number}]成功。"
        };
    }

    /// <summary>
    /// 处理取消中的JavaScript运行时异常
    /// </summary>
    private CancelProcessInstanceResult HandleJsRuntimeExceptionInCancel(JsRuntimeException ex)
    {
        var result = new CancelProcessInstanceResult();
        result.FillCodeResult(ex);
        result.Success = false;
        result.Message = $"取消失败。原因：\"{ex.EventName}\"脚本执行失败。\r\n详情：{ex.Message},出错行数:{ex.Coordinates}";
        return result;
    }

    /// <summary>
    /// 保存流程实例
    /// </summary>
    public void Save(ProcessInstance processInstance)
    {
        if (processInstance == null) 
            throw new ArgumentNullException(nameof(processInstance));
            
        ProcessInstanceStore.AddOrUpdate(processInstance);
        ProcessInstanceStore.SaveChanges();
    }

    /// <summary>
    /// 保存表单
    /// </summary>
    public void SaveForm(int processInstanceId, ProcessInstanceFormSubmit submit)
    {
        var processInstance = GetById(processInstanceId);
        processInstance.MergeForm(submit);

        if (submit.Tags != null)
        {
            _tagManager.UpdateTags(processInstance, new TagsSubmit { Tags = submit.Tags });
        }
        
        if (!string.IsNullOrEmpty(submit.Subject))
        {
            processInstance.Subject = submit.Subject;
        }
        
        if (submit.Priority.HasValue)
        {
            processInstance.Priority = submit.Priority.Value;
        }

        Save(processInstance);
    }

    /// <summary>
    /// 保存表单（通过编号）
    /// </summary>
    public SaveFormResult SaveForm1(string number, ProcessInstanceFormSubmit submit)
    {
        var processInstance = ProcessInstanceStore.Get(number);
        processInstance.ReplaceForm(submit.Form);

        Save(processInstance);
        
        return new SaveFormResult
        {
            Success = true,
            Message = "保存成功!"
        };
    }

    /// <summary>
    /// 保存工作活动表单
    /// </summary>
    public SwfResult SaveForm(int workActivityId, WorkActivityForm formSubmit)
    {
        if (formSubmit == null) 
            throw new ArgumentNullException(nameof(formSubmit));
            
        var workActivity = GetWorkActivityById(workActivityId);
        workActivity.MergeForm(formSubmit);
        
        _tagManager.UpdateTags(workActivity.ProcessInstance, new TagsSubmit { Tags = formSubmit.Tags });
        Save(workActivity);
        
        return new SwfResult
        {
            Code = 0,
            Message = "保存成功."
        };
    }

    /// <summary>
    /// 保存工作活动
    /// </summary>
    public void Save(WorkActivity workActivity)
    {
        if (workActivity == null) 
            throw new ArgumentNullException(nameof(workActivity));
            
        WorkActivityStore.AddOrUpdate(workActivity);
        WorkActivityStore.SaveChanges();
    }

    /// <summary>
    /// 获取流程
    /// </summary>
    public IEnumerable<FlowViewModel> GetFlow(int id, bool all)
    {
        var workActivities = WorkActivityStore.WorkActivities
            .Where(f => f.ProcessInstance.Id == id && f.Status != WorkActivityStatus.CloseByAdmin)
            .OrderByDescending(f => f.CreateTime)
            .ToList()
            .Select(wa => wa.ToFlowViewModel());

        if (!all)
        {
            return GetUniqueFlowViewModels(workActivities);
        }

        return workActivities.OrderByDescending(a => a.Id);
    }

    /// <summary>
    /// 获取唯一的流程视图模型
    /// </summary>
    private static IEnumerable<FlowViewModel> GetUniqueFlowViewModels(IEnumerable<FlowViewModel> workActivities)
    {
        var result = new Dictionary<string, FlowViewModel>();
        
        foreach (var flowViewModel in workActivities)
        {
            var key = $"{flowViewModel.TaskName}_{flowViewModel.DisposeUserName}";
            
            if (result.TryGetValue(key, out var existingViewModel))
            {
                if (flowViewModel.Id > existingViewModel.Id)
                {
                    result[key] = flowViewModel;
                }
            }
            else
            {
                result[key] = flowViewModel;
            }
        }

        return result.Values.OrderByDescending(a => a.Id);
    }

    /// <summary>
    /// 异步删除流程实例
    /// </summary>
    public async Task DeleteAsync(int processInstanceId, IHttpFileManager httpFileManager, UserViewModel userName)
    {
        using var transaction = _transactionFactory.BeginTransactions();
        try
        {
            // 先取消流程实例
            Cancel(processInstanceId, userName);

            var instance = ProcessInstanceStore.ProcessInstances.First(pi => pi.Id == processInstanceId);
            var workActivities = WorkActivityStore.WorkActivities
                .Where(wa => wa.ProcessInstance == instance)
                .ToList();

            // 删除工作活动
            WorkActivityStore.Remove(workActivities);
            await WorkActivityStore.SaveChangesAsync();

            // 删除标签
            await _tagManager.DeleteTags(instance);

            // 删除文件
            await DeleteProcessInstanceFiles(instance, httpFileManager);

            // 删除流程实例
            ProcessInstanceStore.Remove(instance);
            await ProcessInstanceStore.SaveChangesAsync();

            transaction.Commit();
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }

    /// <summary>
    /// 删除流程实例文件
    /// </summary>
    private async Task DeleteProcessInstanceFiles(ProcessInstance instance, IHttpFileManager httpFileManager)
    {
        foreach (var file in instance.Files)
        {
            await httpFileManager.Delete(file.FileId);
            ProcessInstanceStore.RemoveFile(file);
        }
    }

    /// <summary>
    /// 重置到工作任务
    /// </summary>
    public async Task<ResetToWorkTaskResult> ResetToWorkTaskAsync(ProcessInstance instance, ResetToWorkTaskSubmit submit, UserViewModel currentUser)
    {
        var result = new ResetToWorkTaskResult();
        using var transaction = _transactionFactory.BeginTransactions();

        try
        {
            var workflowContext = GetWorkflowContext(instance, currentUser);
            
            if (!ValidateResetToWorkTask(instance, submit, result))
            {
                return result;
            }

            var workTask = await _workTaskStore.GetByIdAsync(submit.WorkTaskId);
            if (workTask.WorkProcess != instance.WorkProcess)
            {
                result.Success = false;
                result.Message = "重置的工作任务与工单属于不同的工作流程，所以无法重置。";
                return result;
            }

            // 取消当前正在执行的工作活动
            CancelCurrentWorkActivities(instance.Id, workflowContext);

            var processInstanceIsComplete = false;
            var lastWorkflowContext = RunToNextNodeWorkflowContext(workflowContext, new CustomerNextWorkTaskSetting
            {
                NextWorkTaskName = workTask.Name,
                Performers = submit.DeposeUsers.ToArray()
            }, ref processInstanceIsComplete);

            if (lastWorkflowContext != workflowContext && lastWorkflowContext.CurrentNode.Auto && !processInstanceIsComplete)
            {
                HandlerAutoNode(lastWorkflowContext, "开始", out processInstanceIsComplete);
            }

            transaction.Commit();
            return result;
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }

    /// <summary>
    /// 验证重置到工作任务
    /// </summary>
    private bool ValidateResetToWorkTask(ProcessInstance instance, ResetToWorkTaskSubmit submit, ResetToWorkTaskResult result)
    {
        if (instance.Status != ProcessInstanceStatus.Processing)
        {
            result.Success = false;
            result.Message = "工单必须处于执行状态才能正确执行。";
            return false;
        }
        return true;
    }

    /// <summary>
    /// 取消当前工作活动
    /// </summary>
    private void CancelCurrentWorkActivities(int instanceId, IWorkflowContext workflowContext)
    {
        var workActivities = WorkActivityStore.GetInProcessWorkActivity(instanceId);
        foreach (var workActivity in workActivities)
        {
            workActivity.Cancel();
            WorkActivityStore.AddOrUpdate(workActivity);
            _interceptorManager.OnWorkActivityChanged(workflowContext, workActivity, _serviceProvider);
        }
    }

    /// <summary>
    /// 重启工作活动
    /// </summary>
    public ResolveResult Restart(WorkActivity workActivity, UserViewModel currentUser)
    {
        var result = new ResolveResult
        {
            Success = true,
            Message = "处理完成."
        };

        if (workActivity.Status == WorkActivityStatus.Complete)
        {
            result.Message = $"活动已经处于{workActivity.Status},无法重启。";
            return result;
        }

        var workflowContext = GetWorkflowContext(workActivity.ProcessInstance, currentUser);
        var workActivities = WorkActivityStore.GetByGroup(workActivity.TaskCreatingGroup);
        var maxId = workActivities.Any() ? workActivities.Max(wa => wa.Id) : -1;

        var afterWorkActivities = GetAfterWorkActivities(workActivity.Id, maxId);

        using var transaction = _transactionFactory.BeginTransactions();
        try
        {
            DeleteAfterWorkActivities(afterWorkActivities);
            CancelCurrentWorkActivities(workActivity, workflowContext);
            
            var performers = workActivities.Select(wa => wa.DisposeUser);
            var nextWorkTask = workActivity.WorkTask;
            var assignResult = GetAssignResult(workflowContext, nextWorkTask, performers);
            
            var nextWorkActivities = BuildWorkActivity(workflowContext, nextWorkTask, workflowContext.ProcessInstance, assignResult);
            WorkActivityStore.AddOrUpdate(nextWorkActivities);
            
            workflowContext.SetAllWorkActivities(nextWorkActivities);
            LogWorkTaskStart(workflowContext, nextWorkTask);
            
            if (!workflowContext.IsDryRun)
            {
                nextWorkTask.OnWorkTaskStart(workflowContext);
            }

            WorkActivityStore.SaveChanges();
            transaction.Commit();

            return result;
        }
        catch (JsRuntimeException jsRuntimeException)
        {
            transaction.Rollback();
            return HandleJsRuntimeExceptionInRestart(jsRuntimeException, result);
        }
        catch (WorkflowDefinedException ex)
        {
            transaction.Rollback();
            HandleWorkflowDefinedExceptionInRestart(workflowContext, ex);
            throw;
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            HandleGenericExceptionInRestart(workflowContext, ex);
            throw;
        }
        finally
        {
            _workflowLogManager.Flush();
            workflowContext.Dispose();
        }
    }

    /// <summary>
    /// 获取后续工作活动
    /// </summary>
    private IEnumerable<WorkActivity> GetAfterWorkActivities(int workActivityId, int maxId)
    {
        if (maxId == -1)
            return Array.Empty<WorkActivity>();

        return WorkActivityStore.WorkActivities.Where(wa => 
            wa.ProcessInstance.Id == workActivityId && wa.Id > maxId);
    }

    /// <summary>
    /// 删除后续工作活动
    /// </summary>
    private void DeleteAfterWorkActivities(IEnumerable<WorkActivity> afterWorkActivities)
    {
        if (afterWorkActivities.Any())
        {
            WorkActivityStore.Delete(afterWorkActivities);
            _workflowLogManager.Log(new WorkflowLog
            {
                Content = $"重启工作活动，它后继的工作活动全部删除",
                LogLevel = LogLevel.Warning,
                Type = WorkflowLogType.System
            });
        }
    }

    /// <summary>
    /// 取消当前工作活动
    /// </summary>
    private void CancelCurrentWorkActivities(WorkActivity workActivity, IWorkflowContext workflowContext)
    {
        var workActivities = WorkActivityStore.GetByGroup(workActivity.TaskCreatingGroup);
        foreach (var wa in workActivities)
        {
            if (wa.Status == WorkActivityStatus.Processing || 
                wa.Status == WorkActivityStatus.Suspend || 
                wa.Status == WorkActivityStatus.UnAssign)
            {
                wa.Cancel();
                WorkActivityStore.AddOrUpdate(wa);
                _interceptorManager.OnWorkActivityChanged(workflowContext, wa, _serviceProvider);
            }
        }
    }

    /// <summary>
    /// 记录工作任务开始
    /// </summary>
    private void LogWorkTaskStart(IWorkflowContext workflowContext, WorkTask nextWorkTask)
    {
        var message = $"{nextWorkTask.Name}执行开始脚本。";
        _workflowLogManager.LogDebug(workflowContext, WorkflowLogType.Script, message);
    }

    /// <summary>
    /// 处理重启中的JavaScript运行时异常
    /// </summary>
    private ResolveResult HandleJsRuntimeExceptionInRestart(JsRuntimeException jsRuntimeException, ResolveResult result)
    {
        _workflowLogManager.LogError(jsRuntimeException);
        result.FillCodeResult(jsRuntimeException);
        result.Success = false;
        result.Message = $"推进工作失败。原因：\"{jsRuntimeException.EventName}\"执行失败。\r\n详情：{jsRuntimeException.Message}。codeLine:{jsRuntimeException.Coordinates}";
        return result;
    }

    /// <summary>
    /// 处理重启中的异常
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="ex">异常</param>
    /// <param name="module">模块名称</param>
    private void HandleExceptionInRestart(IWorkflowContext workflowContext, Exception ex, string module)
    {
        // 如果是WorkflowDefinedException，发送调试信息
        if (ex is WorkflowDefinedException definedEx)
        {
            workflowContext.SendMessageDebugInfo(definedEx.Message, DebuggerType.Error);
        }
        
        _workflowLogManager.LogError(workflowContext, WorkflowLogType.System, ex, module);
    }

    /// <summary>
    /// 处理重启中的工作流定义异常
    /// </summary>
    private void HandleWorkflowDefinedExceptionInRestart(IWorkflowContext workflowContext, WorkflowDefinedException ex)
    {
        HandleExceptionInRestart(workflowContext, ex, WorkProcess.ModuleOnStart);
    }

    /// <summary>
    /// 处理重启中的通用异常
    /// </summary>
    private void HandleGenericExceptionInRestart(IWorkflowContext workflowContext, Exception ex)
    {
        HandleExceptionInRestart(workflowContext, ex, "UNKNOWN-restart");
    }
}
using System.Collections.Generic;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
/// 并行分支节点提交数据
/// </summary>
public class ParallelSplitNodeSubmit : MultiOutputNodeSubmit
{
    // NextNodeNames 属性已在 MultiOutputNodeSubmit 基类中定义
    // NextNodeName 重写已在 MultiOutputNodeSubmit 基类中定义
    protected override string ConstType => WorkflowDefineType.ParallelSplit;
}

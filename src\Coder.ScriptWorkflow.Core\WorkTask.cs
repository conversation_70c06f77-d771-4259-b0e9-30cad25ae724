﻿using System;
using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.WorkTaskCommands;

namespace Coder.ScriptWorkflow;

/// <summary>
///     工作任务，用于定义
/// </summary>
public class WorkTask : SingleOutputNode
{
    private readonly List<WorkTaskCommand> _commands = new();

    private WorkTaskExtendInfo _extendInfo;

    /// <summary>
    ///     构造函数
    /// </summary>
    public WorkTask()
    {
    }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="name">任务name</param>
    /// <param name="workProcess">工作流定义</param>
    /// <exception cref="ArgumentNullException">name 或者 wp 为空</exception>
    public WorkTask(string name, WorkProcess workProcess)
    {
        // ReSharper disable once VirtualMemberCallInConstructor
        Name = name ?? throw new ArgumentNullException(nameof(name));
        // ReSharper disable once VirtualMemberCallInConstructor
        WorkProcess = workProcess ?? throw new ArgumentNullException(nameof(workProcess));
    }

    /// <summary>
    ///     是否在处理本任务的时候，让用户选择下一个处理人。
    /// </summary>
    public NextTaskPerformers NextTaskPerformers { get; set; } = NextTaskPerformers.Auto;

    /// <summary>
    ///     任务的命令
    /// </summary>
    public virtual IList<WorkTaskCommand> Commands => _commands;

    /// <summary>
    ///     分配器，用于指定什么用户可以解决这个任务。
    /// </summary>
    public virtual Assigner Assigner { get; set; }


    /// <summary>
    ///     是否能够自动执行。
    /// </summary>
    public override bool Auto { get; set; } = false;

    /// <summary>
    ///     是否能够放弃
    /// </summary>
    public bool CanGiveUp { get; set; } = false;

    /// <summary>
    ///     form designer
    /// </summary>
    public string FormDesign { get; set; }

    /// <summary>
    ///     当一个Task中的WorkActivity,结束之后会调用这个script
    /// </summary>
    public virtual WorkActivityScript WorkActivityCompleteScript { get; set; }

    /// <summary>
    ///     WorkTask结束之后发生。
    /// </summary>
    public virtual WorkTaskCompleteScript WorkTaskCompleteScript { get; set; }

    /// <summary>
    ///     当WorkTask开始的时候发生
    /// </summary>
    public virtual WorkTaskStartScript WorkTaskStartScript { get; set; }

    /// <summary>
    ///     建议提交。
    /// </summary>
    public string SuggestionComment { get; set; }

    /// <summary>
    /// </summary>
    public WorkTaskExtendInfo ExtendInfo => _extendInfo ??= new WorkTaskExtendInfo();

    /// <summary>
    ///     对Command进行排序。
    /// </summary>
    public void SortCommand()
    {
        if (Commands != null)
            _commands.Sort((a, b) =>
            {
                var r = a.Order.CompareTo(b.Order);
                if (r == 0)
                    return a.Id.CompareTo(b.Id);
                return r;
            });
    }

    /// <summary>
    ///     获取命令。
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="commandName"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public T GetCommand<T>(string commandName) where T : WorkTaskCommand
    {
        if (commandName == null) throw new ArgumentNullException(nameof(commandName));
        var command = Commands.FirstOrDefault(cmd => cmd.Name == commandName);
        if (command == null)
            throw new ArgumentOutOfRangeException(nameof(commandName), $"{commandName}-命令不存在。");
        return command as T;
    }

    /// <summary>
    ///     获取命令。
    /// </summary>
    /// <param name="commandName"></param>
    /// <returns></returns>
    public WorkTaskCommand GetCommand(string commandName)
    {
        if (commandName == null) throw new ArgumentNullException(nameof(commandName));
        var command = Commands.FirstOrDefault(command => command.Name == commandName);
        if (command == null)
            throw new ArgumentOutOfRangeException(nameof(commandName), $"{commandName}-命令不存在。");
        return command;
    }


    /// <summary>
    ///     当前WorkActivity完成事件。
    /// </summary>
    /// <param name="context"></param>
    /// <returns>返回结果，是否进入下一个环节 true 进入下一个workTask，return false。 维持现状</returns>
    internal bool OnWorkActivityComplete(IWorkflowContext context)
    {
        if (WorkActivityCompleteScript == null || string.IsNullOrWhiteSpace(WorkActivityCompleteScript.Script))
            return true;

        return WorkActivityCompleteScript.Invoke(context);
    }

    /// <summary>
    /// </summary>
    /// <param name="workflowContext"></param>
    /// <exception cref="JsRuntimeException"></exception>
    internal void OnWorkTaskComplete(IWorkflowContext workflowContext)
    {
        if (WorkActivityCompleteScript == null || string.IsNullOrWhiteSpace(WorkActivityCompleteScript.Script))
            return;
        WorkTaskCompleteScript.Invoke(workflowContext);
    }

    /// <summary>
    /// </summary>
    /// <param name="previousContext">此时context还没进行切换，因此还是这个WorktTask上一次。</param>
    /// <exception cref="JsRuntimeException"></exception>
    internal void OnWorkTaskStart(IWorkflowContext previousContext)
    {
        WorkTaskStartScript?.Invoke(previousContext);
    }


    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="previous"></param>
    /// <returns></returns>
    internal AssignResult Assign(IWorkflowContext context,
        IEnumerable<WorkActivity> previous)
    {
        try
        {
            var result = Assigner.Assign(context, previous);
            var users = result.Performers.Any()
                ? string.Join(',', result.Performers.Select(_ => _.Type + ":" + _.Key))
                : "无";
            context.Debugger?.SendMessage(context, $"分配范围:{result.ScopeType};分配人：{users}");
            return result;
        }
        catch (JsRuntimeException ex)
        {
            //派生类并不执行脚本，因此由这里进行try-catch
            context.SendJsExceptionDebuggerInfo(ex);

            throw;
        }
    }

    /// <inheritdoc />
    public override void Validate()
    {
        if (Assigner == null) throw new WorkflowDefinedException("分配不能为空。", Name);

        if (WorkProcess == null) throw new WorkflowDefinedException("WorkProcess工作流定义。", Name);

        if (Commands == null || Commands.Count == 0) throw new WorkflowDefinedException("命令不能为空", Name);
    }
}
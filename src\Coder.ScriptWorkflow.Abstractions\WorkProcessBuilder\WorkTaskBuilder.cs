﻿using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.WorkTaskCommands;

namespace Coder.ScriptWorkflow.WorkProcessBuilder;

public class WorkTaskBuilder : NodeBuilder<WorkTaskSubmit>
{
    private AssignSubmit _assignSubmit;
    private AssignScopeType _scope;

    /// <summary>
    /// </summary>
    /// <param name="taskName"></param>
    /// <param name="builder"></param>
    public WorkTaskBuilder(string taskName, WorkProcessBuilder builder)
        : base(taskName, builder)
    {
    }


    public WorkTaskBuilder SetAssignScopeType(AssignScopeType scope)
    {
        _scope = scope;
        return this;
    }

    public WorkTaskBuilder Assign(string name, PerformerType performerType = PerformerType.User
    )
    {
        var assign = _assignSubmit as UsersAssignerSubmit;
        if (assign == null)
        {
            _assignSubmit = assign = new UsersAssignerSubmit();
            assign.Performers = new List<PerformerSubmit>();
        }

        assign.Performers.Add(new PerformerSubmit
        {
            Name = name,
            Key = name,
            Type = performerType
        });

        return this;
    }

    public WorkTaskBuilder SetExecCommand(string name, string script = null)
    {
        Submit.Commands.Add(new WorkTaskScriptCommandSubmit
        {
            Name = name,
            Script = script
        });
        return this;
    }


    protected override void SetValueNodeSubmit(WorkTaskSubmit submit)
    {
        submit.Assigner = _assignSubmit;
        submit.Assigner.AssignScopeType = _scope;
    }
}
﻿using Coder.ScriptWorkflow.ViewModels.Defined.JsonConverts;
using Newtonsoft.Json;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
///     节点提交
/// </summary>
[JsonConverter(typeof(NodeSubmitJsonConverter))]
public abstract class NodeSubmit
{
   

    /// <summary>
    /// </summary>
    protected NodeSubmit()
    {
    }

    /// <summary>
    ///     类型
    /// </summary>
    [JsonProperty("$type")]
    public string Type
    {
        get => ConstType;
        set { }
    }

    protected abstract string ConstType { get; }

    /// <summary>
    ///     id
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     节点名称
    /// </summary>
    public string Name { get; set; }

 

    // Validate 方法已迁移到 DtoTranslator 中，避免重复验证逻辑
}
﻿using System;
using System.Security.Cryptography;

internal static class RandomIdGenerator
{
    /// <summary>
    /// 这里没有 "." 他是关键支付
    /// </summary>
    private static readonly char[] chars = new[]
    {
        '!', '"', '#', '$', '%', '&', '\'', '(', ')', '*',
        '+', ',', '-', '/', '0', '1', '2', '3', '4',
        '5', '6', '7', '8', '9', ':', ';', '<', '=', '>',
        '?', '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
        'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R',
        'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\',
        ']', '^', '_', '`', '{', '|', '}', '~'
    };

    public static string GenerateRandomId(int length)
    {
        if (length <= 0)
            throw new ArgumentException("长度必须大于0", nameof(length));


        return RandomNumberGenerator.GetString(chars, length);
    }
}
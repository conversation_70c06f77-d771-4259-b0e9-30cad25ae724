﻿namespace Coder.ScriptWorkflow.Core.Constants;

/// <summary>
/// 工作流系统常量定义
/// </summary>
public static class WorkflowConstants
{
    /// <summary>
    /// 默认值
    /// </summary>
    public static class Defaults
    {
        public const int DefaultTimeoutSeconds = 300;
        public const int MaxConcurrentOperations = 10;
        public const int DefaultRetryCount = 3;
        public const string DefaultCulture = "zh-CN";
    }

    /// <summary>
    /// 错误消息
    /// </summary>
    public static class ErrorMessages
    {
        public const string WorkflowNotFound = "工作流不存在";
        public const string InvalidStatus = "工作流状态无效";
        public const string UnauthorizedAccess = "无权访问该工作流";
        public const string ValidationFailed = "数据验证失败";
        public const string ScriptExecutionFailed = "脚本执行失败";
    }

    /// <summary>
    /// 配置键名
    /// </summary>
    public static class ConfigKeys
    {
        public const string ConnectionString = "ConnectionStrings:DefaultConnection";
        public const string DatabaseType = "DatabaseType";
        public const string FileSystemHost = "FileSystemHost";
        public const string EnableDebugMode = "EnableDebugMode";
    }

    /// <summary>
    /// 缓存键模板
    /// </summary>
    public static class CacheKeys
    {
        public const string WorkProcessTemplate = "workflow:process:{0}";
        public const string UserPermissionsTemplate = "workflow:permissions:{0}";
        public const string WorkActivityTemplate = "workflow:activity:{0}";
    }

    /// <summary>
    /// 脚本相关常量
    /// </summary>
    public static class Scripts
    {
        public const string DefaultAssignerScript = "return processInstance.Creator";
        public const int MaxScriptLength = 10000;
        public const string ScriptTimeoutErrorMessage = "脚本执行超时";
    }

    /// <summary>
    /// 数据库相关常量
    /// </summary>
    public static class Database
    {
        public const string MigrationHistoryTable = "swf_efmigrationshistory";
        public const int DefaultCommandTimeout = 300;
        public const string DefaultSchemaName = "dbo";
    }
}
﻿using System.Collections.Generic;
using System.Linq;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using NiL.JS.BaseLibrary;
using NiL.JS.Core;
using NiL.JS.Extensions;

namespace Coder.ScriptWorkflow.Decisions;

/// <summary>
///     脚本
/// </summary>
public class ScriptDecision : Decision
{
    /// <summary>
    ///     执行
    /// </summary>
    public string Script { get; set; } = @"//processInstance 流程实例，workActivities 当前工作任务的对应的workActivity。worktasks 在这个节点之前的";

    /// <inheritdoc />
    public override bool Auto
    {
        get => true;

        set { }
    }

    /// <inheritdoc />
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        var context = workflowContext.BuildScriptContext();
        var script = @$"
var __CALLER = () =>{{
            {Script}
            }}";

        var was = new ScriptWorkActivityCollection(workflowContext.AllWorkActivities);
        context.DefineVariable("workActivities").Assign(context.GlobalContext.ProxyValue(was));
        var workTass = GetPrevious(workflowContext);

        context.DefineVariable("workTasks").Assign(workTass.Select(workTask => new ScriptWorkTask(workTask)), context);
        try
        {
            context.Eval(script);
            var concatFunction = context.GetVariable("__CALLER").As<Function>();
            var jsValue = concatFunction.Call(new Arguments());

            nextNode = Judge(jsValue, workTass);
            if (nextNode == null)
                throw new WorkflowDefinedException($"判断器(名称={Name ?? "未命名"}，id={Id})" + jsValue.Value + "不是有效的工作任务。",workflowContext.CurrentNode.Name);

            //永远返回true，因为是二元获取模式，不是Match 就是 Else
            return true;
        }
        catch (JSException ex)
        {
            throw ex.ToJSException(workflowContext, "判断器-" + Name, script);
        }
    }


    private Node Judge(JSValue jsValue, IList<WorkTask> workTasks)
    {
        if (!jsValue.Defined)
            return NextNode;
        if (jsValue.IsNull)
            return NextNode;

        switch (jsValue.ValueType)
        {
            case JSValueType.String:
                var node = workTasks.FirstOrDefault(_ => _.Name == jsValue.Value.ToString());

                return node;
            default:
                return NextNode;
                ;
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    private IList<WorkTask> GetPrevious(IWorkflowContext context)
    {
        var result = new List<WorkTask>();
        var currentNode = (Node)context.StartNode;
        while (currentNode.Id != context.CurrentWorkActivity.WorkTask.Id)
        {
            // 使用 TryNextNode 方法来获取下一个节点
            if (currentNode.TryNextNode(context, out var nextNode))
            {
                currentNode = nextNode;
                if (currentNode is WorkTask workTask) result.Add(workTask);
            }
            else
            {
                // 如果无法获取下一个节点，跳出循环
                break;
            }
        }

        return result;
    }
}
﻿using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
///     开始节点。
/// </summary>
public class StartNodeSubmit : SingleOutputNodeSubmit
{
    public static string StartNodeName = "开始";

    /// <summary>
    /// </summary>
    public StartNodeSubmit()
    {
        Name = StartNodeName;
    }

    protected override string ConstType => WorkflowDefineType.StartNode;
}
﻿using System;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
///     开始节点
/// </summary>
public class StartNode : SingleOutputNode
{
    /// <summary>
    /// </summary>
    public StartNode()
    {
    }

    /// <summary>
    /// </summary>
    /// <param name="nextNode"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public StartNode(Node nextNode, WorkProcess wp) : base(wp)
    {
        NextNode = nextNode ?? throw new ArgumentNullException(nameof(nextNode));
    }

    /// <inheritdoc />
    public override bool Auto
    {
        get => true;
        set
        {
            // throw new ArgumentOutOfRangeException(nameof(value), "StartNode不能设置自动。");
        }
    }

    /// <inheritdoc />
    public override string Name
    {
        get => StartNodeSubmit.StartNodeName;
        set { }
    }
}
﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Coder.ScriptWorkflow.Migrations.Sqlite.Migrations
{
    /// <inheritdoc />
    public partial class parale : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_swf_node_swf_node_NextNodeId",
                table: "swf_node");

            migrationBuilder.DropTable(
                name: "swf_PI_Distribution");

            migrationBuilder.AddColumn<string>(
                name: "ParallelTokenGroup",
                table: "swf_WorkActivity",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ParentWorkActivityId",
                table: "swf_WorkActivity",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CustomJoinScript",
                table: "swf_node",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "JoinCondition",
                table: "swf_node",
                type: "INTEGER",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MultiOutputNodeId",
                table: "swf_node",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WaitForWorkTasks",
                table: "swf_node",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_swf_node_MultiOutputNodeId",
                table: "swf_node",
                column: "MultiOutputNodeId");

            migrationBuilder.AddForeignKey(
                name: "FK_swf_node_swf_node_MultiOutputNodeId",
                table: "swf_node",
                column: "MultiOutputNodeId",
                principalTable: "swf_node",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_swf_node_swf_node_NextNodeId",
                table: "swf_node",
                column: "NextNodeId",
                principalTable: "swf_node",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_swf_node_swf_node_MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.DropForeignKey(
                name: "FK_swf_node_swf_node_NextNodeId",
                table: "swf_node");

            migrationBuilder.DropIndex(
                name: "IX_swf_node_MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "ParallelTokenGroup",
                table: "swf_WorkActivity");

            migrationBuilder.DropColumn(
                name: "ParentWorkActivityId",
                table: "swf_WorkActivity");

            migrationBuilder.DropColumn(
                name: "CustomJoinScript",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "JoinCondition",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "MultiOutputNodeId",
                table: "swf_node");

            migrationBuilder.DropColumn(
                name: "WaitForWorkTasks",
                table: "swf_node");

            migrationBuilder.CreateTable(
                name: "swf_PI_Distribution",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ProcessInstanceId = table.Column<int>(type: "INTEGER", nullable: true),
                    CreateTime = table.Column<string>(type: "TEXT", nullable: false),
                    HasRead = table.Column<bool>(type: "INTEGER", nullable: false),
                    ReadTime = table.Column<string>(type: "TEXT", nullable: true),
                    UserName = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true),
                    UserRealName = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_swf_PI_Distribution", x => x.Id);
                    table.ForeignKey(
                        name: "FK_swf_PI_Distribution_swf_processInstance_ProcessInstanceId",
                        column: x => x.ProcessInstanceId,
                        principalTable: "swf_processInstance",
                        principalColumn: "Id");
                },
                comment: "工作流程分发记录");

            migrationBuilder.CreateIndex(
                name: "IX_swf_PI_Distribution_ProcessInstanceId",
                table: "swf_PI_Distribution",
                column: "ProcessInstanceId");

            migrationBuilder.AddForeignKey(
                name: "FK_swf_node_swf_node_NextNodeId",
                table: "swf_node",
                column: "NextNodeId",
                principalTable: "swf_node",
                principalColumn: "Id");
        }
    }
}

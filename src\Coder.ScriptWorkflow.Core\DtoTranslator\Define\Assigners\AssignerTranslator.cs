﻿using System;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.ViewModels.Defined.Assigners;

namespace Coder.ScriptWorkflow.DtoTranslator.Define.Assigners;

/// <summary>
/// </summary>
public abstract class AssignerTranslator
{
    /// <summary>
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="workTask"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    public virtual Assigner Get(AssignSubmit submit, WorkTask workTask, WorkflowSubmitContext context)
    {
        var assigner = workTask.Assigner;
        var overWrite = context.OverWrite;

        if (!overWrite)
        {
            assigner = CreateEntity();
        }
        else
        {
            //如果类型不同，也是直接创建新的
            if (assigner == null || !TypeIsMatch(assigner))
            {
                workTask.Assigner = CreateEntity();
                assigner = workTask.Assigner;
            }
        }


        assigner.AssignScopeType = submit.AssignScopeType;
        FillToEntity(submit, assigner);

        return assigner;
    }

    public virtual AssignSubmit ToViewModel(Assigner assigner)
    {
        var result = CreateViewModel();
        result.AssignScopeType = assigner.AssignScopeType;
        result.Id = assigner.Id;

        FillToViewModel(assigner, result);
        return result;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected abstract Assigner CreateEntity();

    /// <summary>
    /// </summary>
    /// <returns></returns>
    protected abstract AssignSubmit CreateViewModel();

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="traget"></param>
    protected abstract void FillToEntity(AssignSubmit src, Assigner traget);

    /// <summary>
    /// </summary>
    /// <param name="src"></param>
    /// <param name="traget"></param>
    protected abstract void FillToViewModel(Assigner src, AssignSubmit traget);

    /// <summary>
    /// </summary>
    /// <param name="assigner"></param>
    /// <returns></returns>
    protected abstract bool TypeIsMatch(Assigner assigner);

    /// <summary>
    ///     验证分配器
    /// </summary>
    /// <param name="submit"></param>
    /// <param name="errorMessage"></param>
    /// <returns></returns>
    public abstract bool Validate(AssignSubmit submit, out string errorMessage);

    /// <summary>
    /// </summary>
    /// <param name="assigner"></param>
    /// <returns></returns>
    public static AssignerTranslator Create(Assigner assigner)
    {
        if (assigner == null) throw new ArgumentNullException(nameof(assigner));
        return assigner switch
        {
            ScriptAssigner scriptAssigner => new ScriptAssignerTranslator(),
            UsersAssigner usersAssigner => new UserAssignerTranslator(),
            _ => throw new WorkflowException(assigner.GetType().Name + "没有定义好。")
        };
    }

    /// <summary>
    /// </summary>
    /// <param name="assigner"></param>
    /// <returns></returns>
    /// <exception cref="WorkflowException"></exception>
    public static AssignerTranslator Create(AssignSubmit assigner)
    {
        if (assigner == null)
        {
           throw new ArgumentNullException(nameof(assigner));
        }
        return assigner switch
        {
            ScriptAssignerSubmit scriptAssigner => new ScriptAssignerTranslator(),
            UsersAssignerSubmit usersAssigner => new UserAssignerTranslator(),
            _ => throw new WorkflowException(assigner.GetType().Name + "没有定义好。")
        };
    }
}